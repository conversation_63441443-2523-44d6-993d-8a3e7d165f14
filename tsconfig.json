{"extends": "@tsconfig/strictest/tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "apps/cli/src", "module": "esnext", "moduleResolution": "bundler", "target": "esnext", "lib": ["esnext", "dom"], "types": ["node"], "jsx": "react-jsx", "noImplicitAny": true, "noImplicitThis": true, "resolveJsonModule": true, "esModuleInterop": true, "strictNullChecks": true}, "include": ["apps/cli/src"]}
# Self-Contained Modal Pattern Examples

## Current Problem
Currently using string-based state machine (`currentView: 'main' | 'timeEdit' | 'editEntry'`) which creates tight coupling between App and modals.

## Pattern 1: Props-Based (Controlled Components)
Each modal receives boolean `isOpen` prop and manages internal logic, but parent controls visibility.

```typescript
// App.tsx
function App() {
  const [showTimeEditModal, setShowTimeEditModal] = useState(false);
  const [showEditEntryModal, setShowEditEntryModal] = useState(false);
  const [editingEntry, setEditingEntry] = useState<TimeEntry | null>(null);

  const handleOpenEditEntry = (entry: TimeEntry) => {
    setEditingEntry(entry);
    setShowEditEntryModal(true);
    setShowTimeEditModal(false); // Close other modals
  };

  return (
    <Box>
      {/* Always render modals, let them control their own visibility */}
      <TimeEditModal 
        isOpen={showTimeEditModal}
        onClose={() => setShowTimeEditModal(false)}
        onEditEntry={handleOpenEditEntry}
        entries={detail.entries}
      />
      
      <EditEntryModal
        isOpen={showEditEntryModal && !!editingEntry}
        entry={editingEntry}
        onClose={() => {
          setShowEditEntryModal(false);
          setEditingEntry(null);
        }}
        onSave={handleSaveEntry}
      />

      {/* Main content always rendered */}
      {!showTimeEditModal && !showEditEntryModal && (
        <Box flexDirection="row">
          <Calendar onOpenTimeEdit={() => setShowTimeEditModal(true)} />
          <DayPane />
        </Box>
      )}
    </Box>
  );
}

// TimeEditModal.tsx
function TimeEditModal({ isOpen, onClose, onEditEntry, entries }) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  
  useInput((input, key) => {
    if (!isOpen) return; // Only handle input when open
    
    if (key.escape) {
      onClose();
    }
    // ... other handlers
  });

  if (!isOpen) return null;

  return (
    <Box position="absolute" /* modal overlay styles */>
      {/* modal content */}
    </Box>
  );
}
```

**Pros:**
- Parent has full control over modal state
- Easy to coordinate between modals (close one when opening another)
- Predictable data flow (props down, events up)
- Easy to test

**Cons:**
- Parent needs to manage all modal states
- Some boilerplate in parent component

## Pattern 2: Autonomous (Internal State Only)
Each modal manages its own state completely and exposes imperative methods.

```typescript
// App.tsx
function App() {
  const timeEditModalRef = useRef<TimeEditModalRef>(null);
  const editEntryModalRef = useRef<EditEntryModalRef>(null);

  const handleOpenEditEntry = (entry: TimeEntry) => {
    timeEditModalRef.current?.close();
    editEntryModalRef.current?.open(entry);
  };

  return (
    <Box>
      <TimeEditModal 
        ref={timeEditModalRef}
        entries={detail.entries}
        onEditEntry={handleOpenEditEntry}
      />
      
      <EditEntryModal
        ref={editEntryModalRef}
        onSave={handleSaveEntry}
      />

      <Box flexDirection="row">
        <Calendar onOpenTimeEdit={() => timeEditModalRef.current?.open()} />
        <DayPane />
      </Box>
    </Box>
  );
}

// TimeEditModal.tsx
export type TimeEditModalRef = {
  open: () => void;
  close: () => void;
};

const TimeEditModal = forwardRef<TimeEditModalRef, TimeEditModalProps>((props, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);

  useImperativeHandle(ref, () => ({
    open: () => setIsOpen(true),
    close: () => setIsOpen(false),
  }));
  
  useInput((input, key) => {
    if (!isOpen) return;
    
    if (key.escape) {
      setIsOpen(false); // Close itself
    }
    // ... other handlers
  });

  if (!isOpen) return null;

  return (
    <Box position="absolute">
      {/* modal content */}
    </Box>
  );
});
```

**Pros:**
- Maximum encapsulation - each modal fully self-contained
- Parent doesn't need to manage modal state variables
- Very clean parent component

**Cons:**
- Imperative API (refs) instead of declarative
- Harder to coordinate between modals
- Less predictable for React patterns

## Pattern 3: Hybrid (Internal State + External Control)
Modals have internal state but accept external control props and emit state changes.

```typescript
// App.tsx
function App() {
  const [editingEntry, setEditingEntry] = useState<TimeEntry | null>(null);

  const handleOpenEditEntry = (entry: TimeEntry) => {
    setEditingEntry(entry);
  };

  return (
    <Box>
      <TimeEditModal 
        entries={detail.entries}
        onEditEntry={handleOpenEditEntry}
        onStateChange={(isOpen) => {
          if (isOpen) setEditingEntry(null); // Close edit modal when time modal opens
        }}
      />
      
      <EditEntryModal
        entry={editingEntry}
        onClose={() => setEditingEntry(null)}
        onSave={handleSaveEntry}
      />

      <Box flexDirection="row">
        <Calendar />
        <DayPane />
      </Box>
    </Box>
  );
}

// TimeEditModal.tsx  
function TimeEditModal({ entries, onEditEntry, onStateChange }) {
  const [isOpen, setIsOpen] = useState(false);
  
  const handleOpen = () => {
    setIsOpen(true);
    onStateChange?.(true);
  };
  
  const handleClose = () => {
    setIsOpen(false);
    onStateChange?.(false);
  };

  // Expose open method somehow (could be through a custom hook)
  
  useInput((input, key) => {
    if (!isOpen) return;
    
    if (key.escape) {
      handleClose();
    }
    // ... other handlers
  });

  if (!isOpen) return null;

  return (
    <Box position="absolute">
      {/* modal content */}
    </Box>
  );
}
```

**Pros:**
- Good balance of encapsulation and coordination
- Each modal controls its own state
- Parent can react to state changes when needed

**Cons:**
- More complex API
- Still need some coordination logic in parent

## Recommendation

For your tempo-cli use case, I'd recommend **Pattern 1 (Props-Based)** because:

1. **Clear data flow**: Easy to understand and debug
2. **Good coordination**: Simple to ensure only one modal is open at a time
3. **React-friendly**: Follows standard React patterns
4. **Testable**: Easy to test modal interactions
5. **Current structure**: Minimal changes from your existing code

The key changes would be:
- Replace `currentView` string with individual boolean states
- Each modal renders conditionally based on its boolean
- App coordinates between modals by managing their boolean states
- Main content shows when no modals are active

Would you like me to implement Pattern 1, or do you prefer a different approach?
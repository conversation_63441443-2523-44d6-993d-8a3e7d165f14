{"version": "1.0.0", "clients": {"TempoApi": {"language": "TypeScript", "typeAccessModifier": "Public", "structuredMimeTypes": ["application/json", "text/plain;q=0.9", "application/x-www-form-urlencoded;q=0.2", "multipart/form-data;q=0.1"], "clientNamespaceName": "Tempo", "usesBackingStore": false, "includeAdditionalData": true, "excludeBackwardCompatible": false, "disabledValidationRules": [], "descriptionLocation": "./packages/tempo/tempo-openapi.yaml", "includePatterns": ["/4/worklogs"], "excludePatterns": [], "outputPath": "./packages/tempo"}}, "plugins": {}}
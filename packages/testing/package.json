{"name": "@repo/testing", "private": true, "version": "0.0.0", "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules"}, "main": "./index.js", "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "catalog:", "@vitejs/plugin-react": "catalog:testing", "@vitest/coverage-v8": "catalog:testing", "jsdom": "catalog:testing", "vitest": "catalog:testing"}, "peerDependencies": {"@vitest/coverage-v8": "catalog:testing"}}
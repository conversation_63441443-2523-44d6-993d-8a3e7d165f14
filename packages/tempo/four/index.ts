/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { type WorklogsRequestBuilder, WorklogsRequestBuilderRequestsMetadata } from './worklogs/index.js';
// @ts-ignore
import { type BaseRequestBuilder, type KeysToExcludeForNavigationMetadata, type NavigationMetadata } from '@microsoft/kiota-abstractions';

/**
 * Builds and executes requests for operations under /4
 */
export interface FourRequestBuilder extends BaseRequestBuilder<FourRequestBuilder> {
    /**
     * The worklogs property
     */
    get worklogs(): WorklogsRequestBuilder;
}
/**
 * Uri template for the request builder.
 */
export const FourRequestBuilderUriTemplate = "{+baseurl}/4";
/**
 * Metadata for all the navigation properties in the request builder.
 */
export const FourRequestBuilderNavigationMetadata: Record<Exclude<keyof FourRequestBuilder, KeysToExcludeForNavigationMetadata>, NavigationMetadata> = {
    worklogs: {
        requestsMetadata: WorklogsRequestBuilderRequestsMetadata,
    },
};
/* tslint:enable */
/* eslint-enable */

/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { createPageableWorklogFromDiscriminatorValue, createWorklogFromDiscriminatorValue, serializeWorklog, serializeWorklogInput, type PageableWorklog, type Worklog, type WorklogInput } from '../../models/index.js';
// @ts-ignore
import { type BaseRequestBuilder, type Parsable, type ParsableFactory, type RequestConfiguration, type RequestInformation, type RequestsMetadata } from '@microsoft/kiota-abstractions';

export type GetOrderByQueryParameterType = (typeof GetOrderByQueryParameterTypeObject)[keyof typeof GetOrderByQueryParameterTypeObject];
/**
 * Builds and executes requests for operations under /4/worklogs
 */
export interface WorklogsRequestBuilder extends BaseRequestBuilder<WorklogsRequestBuilder> {
    /**
     * Retrieves a list of Worklogs that matches the given search parameters
     * @param requestConfiguration Configuration for the request such as headers, query parameters, and middleware options.
     * @returns {Promise<PageableWorklog>}
     */
     get(requestConfiguration?: RequestConfiguration<WorklogsRequestBuilderGetQueryParameters> | undefined) : Promise<PageableWorklog | undefined>;
    /**
     * Creates a new Worklog using the provided input and returns the newly created Worklog
     * @param body The request body
     * @param requestConfiguration Configuration for the request such as headers, query parameters, and middleware options.
     * @returns {Promise<Worklog>}
     */
     post(body: WorklogInput, requestConfiguration?: RequestConfiguration<object> | undefined) : Promise<Worklog | undefined>;
    /**
     * Retrieves a list of Worklogs that matches the given search parameters
     * @param requestConfiguration Configuration for the request such as headers, query parameters, and middleware options.
     * @returns {RequestInformation}
     */
     toGetRequestInformation(requestConfiguration?: RequestConfiguration<WorklogsRequestBuilderGetQueryParameters> | undefined) : RequestInformation;
    /**
     * Creates a new Worklog using the provided input and returns the newly created Worklog
     * @param body The request body
     * @param requestConfiguration Configuration for the request such as headers, query parameters, and middleware options.
     * @returns {RequestInformation}
     */
     toPostRequestInformation(body: WorklogInput, requestConfiguration?: RequestConfiguration<object> | undefined) : RequestInformation;
}
/**
 * Retrieves a list of Worklogs that matches the given search parameters
 */
export interface WorklogsRequestBuilderGetQueryParameters {
    /**
     * Retrieve results starting with this date
     */
    from?: string;
    /**
     * Retrieve only worklogs for the given issue ids
     */
    issueId?: number[];
    /**
     * Limit the number of elements on the response
     */
    limit?: number;
    /**
     * Skip over a number of elements by specifying an offset value for the query
     */
    offset?: number;
    /**
     * Order results by the specified field descending. If no order is specified, results will by default be ordered by START_DATE_TIME and ID ascending
     */
    orderBy?: GetOrderByQueryParameterType;
    /**
     * Retrieve only worklogs for the given project ids
     */
    projectId?: number[];
    /**
     * Retrieve results up to and including this date
     */
    to?: string;
    /**
     * Retrieve results that have been updated from this date(e.g "2023-11-16") or date time (e.g "2023-11-06T16:48:59Z")
     */
    updatedFrom?: string;
}
/**
 * Uri template for the request builder.
 */
export const WorklogsRequestBuilderUriTemplate = "{+baseurl}/4/worklogs{?from*,issueId*,limit*,offset*,orderBy*,projectId*,to*,updatedFrom*}";
export const GetOrderByQueryParameterTypeObject = {
    ID: "ID",
    START_DATE_TIME: "START_DATE_TIME",
    UPDATED: "UPDATED",
} as const;
/**
 * Metadata for all the requests in the request builder.
 */
export const WorklogsRequestBuilderRequestsMetadata: RequestsMetadata = {
    get: {
        uriTemplate: WorklogsRequestBuilderUriTemplate,
        responseBodyContentType: "application/json",
        adapterMethodName: "send",
        responseBodyFactory:  createPageableWorklogFromDiscriminatorValue,
    },
    post: {
        uriTemplate: WorklogsRequestBuilderUriTemplate,
        responseBodyContentType: "application/json",
        adapterMethodName: "send",
        responseBodyFactory:  createWorklogFromDiscriminatorValue,
        requestBodyContentType: "application/json",
        requestBodySerializer: serializeWorklogInput,
        requestInformationContentSetMethod: "setContentFromParsable",
    },
};
/* tslint:enable */
/* eslint-enable */

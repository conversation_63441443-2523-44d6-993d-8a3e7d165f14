/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { FourRequestBuilderNavigationMetadata, type FourRequestBuilder } from './four/index.js';
// @ts-ignore
import { apiClientProxifier, ParseNodeFactoryRegistry, SerializationWriterFactoryRegistry, type BaseRequestBuilder, type KeysToExcludeForNavigationMetadata, type NavigationMetadata, type RequestAdapter } from '@microsoft/kiota-abstractions';

/**
 * Instantiates a new {@link TempoApi} and sets the default values.
 * @param requestAdapter The request adapter to use to execute the requests.
 */
// @ts-ignore
export function createTempoApi(requestAdapter: RequestAdapter) {
    if (requestAdapter === undefined) {
        throw new Error("requestAdapter cannot be undefined");
    }
    const serializationWriterFactory = requestAdapter.getSerializationWriterFactory() as SerializationWriterFactoryRegistry;
    const parseNodeFactoryRegistry = requestAdapter.getParseNodeFactory() as ParseNodeFactoryRegistry;
    const backingStoreFactory = requestAdapter.getBackingStoreFactory();
    
    if (parseNodeFactoryRegistry.registerDefaultDeserializer) {
    }
    
    if (serializationWriterFactory.registerDefaultSerializer) {
    }
    
    if (requestAdapter.baseUrl === undefined || requestAdapter.baseUrl === null || requestAdapter.baseUrl === "") {
        requestAdapter.baseUrl = "https://api.tempo.io";
    }
    const pathParameters: Record<string, unknown> = {
        "baseurl": requestAdapter.baseUrl,
    };
    return apiClientProxifier<TempoApi>(requestAdapter, pathParameters, TempoApiNavigationMetadata, undefined);
}
/**
 * The main entry point of the SDK, exposes the configuration and the fluent API.
 */
export interface TempoApi extends BaseRequestBuilder<TempoApi> {
    /**
     * The Four property
     */
    get four(): FourRequestBuilder;
}
/**
 * Uri template for the request builder.
 */
export const TempoApiUriTemplate = "{+baseurl}";
/**
 * Metadata for all the navigation properties in the request builder.
 */
export const TempoApiNavigationMetadata: Record<Exclude<keyof TempoApi, KeysToExcludeForNavigationMetadata>, NavigationMetadata> = {
    four: {
        navigationMetadata: FourRequestBuilderNavigationMetadata,
    },
};
/* tslint:enable */
/* eslint-enable */

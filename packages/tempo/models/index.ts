/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { type AdditionalDataHolder, type DateOnly, type Parsable, type ParseNode, type SerializationWriter } from '@microsoft/kiota-abstractions';

/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {Issue}
 */
// @ts-ignore
export function createIssueFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoIssue;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {PageableMetadata}
 */
// @ts-ignore
export function createPageableMetadataFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoPageableMetadata;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {PageableWorklog}
 */
// @ts-ignore
export function createPageableWorklogFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoPageableWorklog;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {SelfListWorkAttributeValue}
 */
// @ts-ignore
export function createSelfListWorkAttributeValueFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoSelfListWorkAttributeValue;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {User}
 */
// @ts-ignore
export function createUserFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoUser;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {WorkAttributeValue}
 */
// @ts-ignore
export function createWorkAttributeValueFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoWorkAttributeValue;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {WorkAttributeValueInput}
 */
// @ts-ignore
export function createWorkAttributeValueInputFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoWorkAttributeValueInput;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {Worklog}
 */
// @ts-ignore
export function createWorklogFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoWorklog;
}
/**
 * Creates a new instance of the appropriate class based on discriminator value
 * @param parseNode The parse node to use to read the discriminator value and create the object
 * @returns {WorklogInput}
 */
// @ts-ignore
export function createWorklogInputFromDiscriminatorValue(parseNode: ParseNode | undefined) : ((instance?: Parsable) => Record<string, (node: ParseNode) => void>) {
    return deserializeIntoWorklogInput;
}
/**
 * The deserialization information for the current model
 * @param Issue The instance to deserialize into.
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoIssue(issue: Partial<Issue> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "id": n => { issue.id = n.getNumberValue(); },
        "self": n => { issue.self = n.getStringValue(); },
    }
}
/**
 * The deserialization information for the current model
 * @param PageableMetadata The instance to deserialize into.
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoPageableMetadata(pageableMetadata: Partial<PageableMetadata> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "count": n => { pageableMetadata.count = n.getNumberValue(); },
        "limit": n => { pageableMetadata.limit = n.getNumberValue(); },
        "next": n => { pageableMetadata.next = n.getStringValue(); },
        "offset": n => { pageableMetadata.offset = n.getNumberValue(); },
        "previous": n => { pageableMetadata.previous = n.getStringValue(); },
    }
}
/**
 * The deserialization information for the current model
 * @param PageableWorklog The instance to deserialize into.
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoPageableWorklog(pageableWorklog: Partial<PageableWorklog> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "metadata": n => { pageableWorklog.metadata = n.getObjectValue<PageableMetadata>(createPageableMetadataFromDiscriminatorValue); },
        "results": n => { pageableWorklog.results = n.getCollectionOfObjectValues<Worklog>(createWorklogFromDiscriminatorValue); },
        "self": n => { pageableWorklog.self = n.getStringValue(); },
    }
}
/**
 * The deserialization information for the current model
 * @param SelfListWorkAttributeValue The instance to deserialize into.
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoSelfListWorkAttributeValue(selfListWorkAttributeValue: Partial<SelfListWorkAttributeValue> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "self": n => { selfListWorkAttributeValue.self = n.getStringValue(); },
        "values": n => { selfListWorkAttributeValue.values = n.getCollectionOfObjectValues<WorkAttributeValue>(createWorkAttributeValueFromDiscriminatorValue); },
    }
}
/**
 * The deserialization information for the current model
 * @param User The instance to deserialize into.
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoUser(user: Partial<User> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "accountId": n => { user.accountId = n.getStringValue(); },
        "self": n => { user.self = n.getStringValue(); },
    }
}
/**
 * The deserialization information for the current model
 * @param WorkAttributeValue The instance to deserialize into.
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoWorkAttributeValue(workAttributeValue: Partial<WorkAttributeValue> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "key": n => { workAttributeValue.key = n.getStringValue(); },
        "value": n => { workAttributeValue.value = n.getStringValue(); },
    }
}
/**
 * The deserialization information for the current model
 * @param WorkAttributeValueInput The instance to deserialize into.
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoWorkAttributeValueInput(workAttributeValueInput: Partial<WorkAttributeValueInput> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "key": n => { workAttributeValueInput.key = n.getStringValue(); },
        "value": n => { workAttributeValueInput.value = n.getStringValue(); },
    }
}
/**
 * The deserialization information for the current model
 * @param Worklog The instance to deserialize into.
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoWorklog(worklog: Partial<Worklog> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "attributes": n => { worklog.attributes = n.getObjectValue<SelfListWorkAttributeValue>(createSelfListWorkAttributeValueFromDiscriminatorValue); },
        "author": n => { worklog.author = n.getObjectValue<User>(createUserFromDiscriminatorValue); },
        "billableSeconds": n => { worklog.billableSeconds = n.getNumberValue(); },
        "createdAt": n => { worklog.createdAt = n.getStringValue(); },
        "description": n => { worklog.description = n.getStringValue(); },
        "issue": n => { worklog.issue = n.getObjectValue<Issue>(createIssueFromDiscriminatorValue); },
        "self": n => { worklog.self = n.getStringValue(); },
        "startDate": n => { worklog.startDate = n.getDateOnlyValue(); },
        "startDateTimeUtc": n => { worklog.startDateTimeUtc = n.getStringValue(); },
        "startTime": n => { worklog.startTime = n.getStringValue(); },
        "tempoWorklogId": n => { worklog.tempoWorklogId = n.getNumberValue(); },
        "timeSpentSeconds": n => { worklog.timeSpentSeconds = n.getNumberValue(); },
        "updatedAt": n => { worklog.updatedAt = n.getStringValue(); },
    }
}
/**
 * The deserialization information for the current model
 * @param WorklogInput The instance to deserialize into.
 * @returns {Record<string, (node: ParseNode) => void>}
 */
// @ts-ignore
export function deserializeIntoWorklogInput(worklogInput: Partial<WorklogInput> | undefined = {}) : Record<string, (node: ParseNode) => void> {
    return {
        "attributes": n => { worklogInput.attributes = n.getCollectionOfObjectValues<WorkAttributeValueInput>(createWorkAttributeValueInputFromDiscriminatorValue); },
        "authorAccountId": n => { worklogInput.authorAccountId = n.getStringValue(); },
        "billableSeconds": n => { worklogInput.billableSeconds = n.getNumberValue(); },
        "bypassPeriodClosuresAndApprovals": n => { worklogInput.bypassPeriodClosuresAndApprovals = n.getBooleanValue(); },
        "description": n => { worklogInput.description = n.getStringValue(); },
        "issueId": n => { worklogInput.issueId = n.getNumberValue(); },
        "remainingEstimateSeconds": n => { worklogInput.remainingEstimateSeconds = n.getNumberValue(); },
        "startDate": n => { worklogInput.startDate = n.getDateOnlyValue(); },
        "startTime": n => { worklogInput.startTime = n.getStringValue(); },
        "timeSpentSeconds": n => { worklogInput.timeSpentSeconds = n.getNumberValue(); },
    }
}
/**
 * The Issue associated to this worklog
 */
export interface Issue extends AdditionalDataHolder, Parsable {
    /**
     * The id of the issue
     */
    id?: number | null;
    /**
     * A permanent link to this resource
     */
    self?: string | null;
}
export interface PageableMetadata extends AdditionalDataHolder, Parsable {
    /**
     * The number of results returned on this page
     */
    count?: number | null;
    /**
     * Maximum number of results on each page
     */
    limit?: number | null;
    /**
     * A link to the next page of results, if applicable
     */
    next?: string | null;
    /**
     * Number of skipped results
     */
    offset?: number | null;
    /**
     * A link to the previous page of results, if applicable
     */
    previous?: string | null;
}
export interface PageableWorklog extends AdditionalDataHolder, Parsable {
    /**
     * The metadata property
     */
    metadata?: PageableMetadata | null;
    /**
     * The results property
     */
    results?: Worklog[] | null;
    /**
     * A permanent link to this resource
     */
    self?: string | null;
}
/**
 * The list of work attribute of this `Worklog`
 */
export interface SelfListWorkAttributeValue extends AdditionalDataHolder, Parsable {
    /**
     * A permanent link to this resource
     */
    self?: string | null;
    /**
     * A group of links referencing this resource
     */
    values?: WorkAttributeValue[] | null;
}
/**
 * Serializes information the current object
 * @param isSerializingDerivedType A boolean indicating whether the serialization is for a derived type.
 * @param Issue The instance to serialize from.
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeIssue(writer: SerializationWriter, issue: Partial<Issue> | undefined | null = {}, isSerializingDerivedType: boolean = false) : void {
    if (!issue || isSerializingDerivedType) { return; }
    writer.writeNumberValue("id", issue.id);
    writer.writeStringValue("self", issue.self);
    writer.writeAdditionalData(issue.additionalData);
}
/**
 * Serializes information the current object
 * @param isSerializingDerivedType A boolean indicating whether the serialization is for a derived type.
 * @param PageableMetadata The instance to serialize from.
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializePageableMetadata(writer: SerializationWriter, pageableMetadata: Partial<PageableMetadata> | undefined | null = {}, isSerializingDerivedType: boolean = false) : void {
    if (!pageableMetadata || isSerializingDerivedType) { return; }
    writer.writeNumberValue("count", pageableMetadata.count);
    writer.writeNumberValue("limit", pageableMetadata.limit);
    writer.writeStringValue("next", pageableMetadata.next);
    writer.writeNumberValue("offset", pageableMetadata.offset);
    writer.writeStringValue("previous", pageableMetadata.previous);
    writer.writeAdditionalData(pageableMetadata.additionalData);
}
/**
 * Serializes information the current object
 * @param isSerializingDerivedType A boolean indicating whether the serialization is for a derived type.
 * @param PageableWorklog The instance to serialize from.
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializePageableWorklog(writer: SerializationWriter, pageableWorklog: Partial<PageableWorklog> | undefined | null = {}, isSerializingDerivedType: boolean = false) : void {
    if (!pageableWorklog || isSerializingDerivedType) { return; }
    writer.writeObjectValue<PageableMetadata>("metadata", pageableWorklog.metadata, serializePageableMetadata);
    writer.writeCollectionOfObjectValues<Worklog>("results", pageableWorklog.results, serializeWorklog);
    writer.writeStringValue("self", pageableWorklog.self);
    writer.writeAdditionalData(pageableWorklog.additionalData);
}
/**
 * Serializes information the current object
 * @param isSerializingDerivedType A boolean indicating whether the serialization is for a derived type.
 * @param SelfListWorkAttributeValue The instance to serialize from.
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeSelfListWorkAttributeValue(writer: SerializationWriter, selfListWorkAttributeValue: Partial<SelfListWorkAttributeValue> | undefined | null = {}, isSerializingDerivedType: boolean = false) : void {
    if (!selfListWorkAttributeValue || isSerializingDerivedType) { return; }
    writer.writeStringValue("self", selfListWorkAttributeValue.self);
    writer.writeCollectionOfObjectValues<WorkAttributeValue>("values", selfListWorkAttributeValue.values, serializeWorkAttributeValue);
    writer.writeAdditionalData(selfListWorkAttributeValue.additionalData);
}
/**
 * Serializes information the current object
 * @param isSerializingDerivedType A boolean indicating whether the serialization is for a derived type.
 * @param User The instance to serialize from.
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeUser(writer: SerializationWriter, user: Partial<User> | undefined | null = {}, isSerializingDerivedType: boolean = false) : void {
    if (!user || isSerializingDerivedType) { return; }
    writer.writeStringValue("accountId", user.accountId);
    writer.writeStringValue("self", user.self);
    writer.writeAdditionalData(user.additionalData);
}
/**
 * Serializes information the current object
 * @param isSerializingDerivedType A boolean indicating whether the serialization is for a derived type.
 * @param WorkAttributeValue The instance to serialize from.
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeWorkAttributeValue(writer: SerializationWriter, workAttributeValue: Partial<WorkAttributeValue> | undefined | null = {}, isSerializingDerivedType: boolean = false) : void {
    if (!workAttributeValue || isSerializingDerivedType) { return; }
    writer.writeStringValue("key", workAttributeValue.key);
    writer.writeStringValue("value", workAttributeValue.value);
    writer.writeAdditionalData(workAttributeValue.additionalData);
}
/**
 * Serializes information the current object
 * @param isSerializingDerivedType A boolean indicating whether the serialization is for a derived type.
 * @param WorkAttributeValueInput The instance to serialize from.
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeWorkAttributeValueInput(writer: SerializationWriter, workAttributeValueInput: Partial<WorkAttributeValueInput> | undefined | null = {}, isSerializingDerivedType: boolean = false) : void {
    if (!workAttributeValueInput || isSerializingDerivedType) { return; }
    writer.writeStringValue("key", workAttributeValueInput.key);
    writer.writeStringValue("value", workAttributeValueInput.value);
    writer.writeAdditionalData(workAttributeValueInput.additionalData);
}
/**
 * Serializes information the current object
 * @param isSerializingDerivedType A boolean indicating whether the serialization is for a derived type.
 * @param Worklog The instance to serialize from.
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeWorklog(writer: SerializationWriter, worklog: Partial<Worklog> | undefined | null = {}, isSerializingDerivedType: boolean = false) : void {
    if (!worklog || isSerializingDerivedType) { return; }
    writer.writeObjectValue<SelfListWorkAttributeValue>("attributes", worklog.attributes, serializeSelfListWorkAttributeValue);
    writer.writeObjectValue<User>("author", worklog.author, serializeUser);
    writer.writeNumberValue("billableSeconds", worklog.billableSeconds);
    writer.writeStringValue("createdAt", worklog.createdAt);
    writer.writeStringValue("description", worklog.description);
    writer.writeObjectValue<Issue>("issue", worklog.issue, serializeIssue);
    writer.writeStringValue("self", worklog.self);
    writer.writeDateOnlyValue("startDate", worklog.startDate);
    writer.writeStringValue("startDateTimeUtc", worklog.startDateTimeUtc);
    writer.writeStringValue("startTime", worklog.startTime);
    writer.writeNumberValue("tempoWorklogId", worklog.tempoWorklogId);
    writer.writeNumberValue("timeSpentSeconds", worklog.timeSpentSeconds);
    writer.writeStringValue("updatedAt", worklog.updatedAt);
    writer.writeAdditionalData(worklog.additionalData);
}
/**
 * Serializes information the current object
 * @param isSerializingDerivedType A boolean indicating whether the serialization is for a derived type.
 * @param WorklogInput The instance to serialize from.
 * @param writer Serialization writer to use to serialize this model
 */
// @ts-ignore
export function serializeWorklogInput(writer: SerializationWriter, worklogInput: Partial<WorklogInput> | undefined | null = {}, isSerializingDerivedType: boolean = false) : void {
    if (!worklogInput || isSerializingDerivedType) { return; }
    writer.writeCollectionOfObjectValues<WorkAttributeValueInput>("attributes", worklogInput.attributes, serializeWorkAttributeValueInput);
    writer.writeStringValue("authorAccountId", worklogInput.authorAccountId);
    writer.writeNumberValue("billableSeconds", worklogInput.billableSeconds);
    writer.writeBooleanValue("bypassPeriodClosuresAndApprovals", worklogInput.bypassPeriodClosuresAndApprovals);
    writer.writeStringValue("description", worklogInput.description);
    writer.writeNumberValue("issueId", worklogInput.issueId);
    writer.writeNumberValue("remainingEstimateSeconds", worklogInput.remainingEstimateSeconds);
    writer.writeDateOnlyValue("startDate", worklogInput.startDate);
    writer.writeStringValue("startTime", worklogInput.startTime);
    writer.writeNumberValue("timeSpentSeconds", worklogInput.timeSpentSeconds);
    writer.writeAdditionalData(worklogInput.additionalData);
}
/**
 * The author of this `worklog`
 */
export interface User extends AdditionalDataHolder, Parsable {
    /**
     * A unique identifier of the user in Jira
     */
    accountId?: string | null;
    /**
     * A permanent link to this resource
     */
    self?: string | null;
}
export interface WorkAttributeValue extends AdditionalDataHolder, Parsable {
    /**
     * The key of the `WorkAttributeValue`
     */
    key?: string | null;
    /**
     * The value of the `WorkAttributeValue`
     */
    value?: string | null;
}
/**
 * The list of work attribute of this `Worklog`
 */
export interface WorkAttributeValueInput extends AdditionalDataHolder, Parsable {
    /**
     * The key of the `WorkAttributeValue`
     */
    key?: string | null;
    /**
     * The value of the `WorkAttributeValue`
     */
    value?: string | null;
}
export interface Worklog extends AdditionalDataHolder, Parsable {
    /**
     * The list of work attribute of this `Worklog`
     */
    attributes?: SelfListWorkAttributeValue | null;
    /**
     * The author of this `worklog`
     */
    author?: User | null;
    /**
     * The amount of seconds billable
     */
    billableSeconds?: number | null;
    /**
     * The date and time when this `Worklog` was created
     */
    createdAt?: string | null;
    /**
     * The description of the `Worklog`
     */
    description?: string | null;
    /**
     * The Issue associated to this worklog
     */
    issue?: Issue | null;
    /**
     * A permanent link to this resource
     */
    self?: string | null;
    /**
     * The start date of the `Worklog`
     */
    startDate?: DateOnly | null;
    /**
     * The start date and time combination of the `Worklog` in UTC. Conversion is done using the worker's browser timezone at the time of worklog creation, when available, otherwise the worker's Atlassian profile timezone is used. Note that if the worker's Atlassian profile timezone is hidden via privacy settings, then Jira returns the instance default timezone to us instead.
     */
    startDateTimeUtc?: string | null;
    /**
     * The start time of the `Worklog`
     */
    startTime?: string | null;
    /**
     * The worklog Id in the tempo app
     */
    tempoWorklogId?: number | null;
    /**
     * The total amount of time spent in seconds`
     */
    timeSpentSeconds?: number | null;
    /**
     * The last date and time when this `Worklog` was updated
     */
    updatedAt?: string | null;
}
export interface WorklogInput extends AdditionalDataHolder, Parsable {
    /**
     * The list of work attribute of this `Worklog`
     */
    attributes?: WorkAttributeValueInput[] | null;
    /**
     * The Account id of the user author
     */
    authorAccountId?: string | null;
    /**
     * The amount of seconds billable
     */
    billableSeconds?: number | null;
    /**
     * Bypass period closures and approvals. If the value is 'true' and the period is closed, the worklog will be created. You need the Tempo Administrator permission and Override Mode enabled.
     */
    bypassPeriodClosuresAndApprovals?: boolean | null;
    /**
     * The description of the `Worklog`
     */
    description?: string | null;
    /**
     * The id of the issue associated to this worklog
     */
    issueId?: number | null;
    /**
     * The total amount of estimated remaining seconds`
     */
    remainingEstimateSeconds?: number | null;
    /**
     * The start date of the `Worklog`
     */
    startDate?: DateOnly | null;
    /**
     * The start time of the `Worklog`
     */
    startTime?: string | null;
    /**
     * The total amount of time spent in seconds`
     */
    timeSpentSeconds?: number | null;
}
/* tslint:enable */
/* eslint-enable */

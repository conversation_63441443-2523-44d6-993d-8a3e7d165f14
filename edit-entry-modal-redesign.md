# EditEntryModal Form Redesign

## Current Issues
- Labels and values are displayed inline (e.g., "Ticket: {ticket}")
- No proper text input components - just text display with cursor indicator
- Poor visual separation between form elements  
- Lacks professional form aesthetics
- Manual text input handling instead of using proper input components

## Design Goals
- Create a professional-looking form with clear label/field separation
- Use proper text input components (ink-text-input)
- Maintain keyboard navigation functionality
- Improve visual hierarchy and spacing
- Keep the modal responsive and accessible

## Proposed Layout

### Form Structure
```
┌─────────────────────────────────────────┐
│             Edit Time Entry             │
├─────────────────────────────────────────┤
│                                         │
│ Ticket:                                 │
│ ┌─────────────────────────────────────┐ │
│ │ ABC-123                             │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ Title:                                  │
│ ┌─────────────────────────────────────┐ │
│ │ Fix login bug                       │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ Description:                            │
│ ┌─────────────────────────────────────┐ │
│ │ Updated authentication logic        │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ Hours:                                  │
│ ┌─────────────────────────────────────┐ │
│ │ 2.5                                 │ │
│ └─────────────────────────────────────┘ │
│                                         │
│              [ Save ]                   │
│                                         │
│     ESC to cancel • TAB/↑↓ to navigate │
│            • ENTER to save              │
└─────────────────────────────────────────┘
```

## Implementation Plan

### 1. Dependencies
Add `ink-text-input` to `apps/cli/package.json`:
```json
"dependencies": {
  // ... existing dependencies
  "ink-text-input": "^5.0.1"
}
```

### 2. Component Changes

#### Imports
```typescript
import { Box, Text, useInput } from 'ink';
import TextInput from 'ink-text-input';
```

#### State Management
- Keep existing state variables: `ticket`, `title`, `description`, `hours`, `focusIndex`
- Simplify input handling by using TextInput's built-in onChange handlers

#### Layout Improvements
- Separate labels from input fields
- Use consistent spacing between form elements
- Style labels with distinct color (e.g., gray or cyan)
- Style input fields with borders or background colors
- Maintain focus indicators for keyboard navigation

#### Focus Management
- Adapt focus handling to work with TextInput components
- Ensure Tab/Arrow key navigation works smoothly
- Highlight focused input fields appropriately

### 3. Styling Specifications

#### Labels
- Color: `gray` or `cyan`
- Bold: `false` 
- Positioned above each input field
- Consistent spacing

#### Input Fields  
- Use TextInput component with built-in styling
- Focus indication through TextInput's focus prop
- Proper cursor handling built into component
- Maintain existing validation (e.g., numeric for hours)

#### Container
- Keep existing modal backdrop and border
- Adjust internal padding for better form spacing
- Ensure responsive width for various terminal sizes

#### Save Button
- Maintain current styling with focus indication
- Keep keyboard accessibility

### 4. Keyboard Navigation
- Tab/Shift+Tab: Navigate between fields
- Up/Down arrows: Navigate between fields
- Enter on focused input: Move to next field
- Enter on Save button: Submit form
- Escape: Cancel and close modal

### 5. Validation
- Hours field: Ensure numeric input only
- Required field indicators (if needed)
- Error handling for invalid inputs

## Files to Modify

1. **apps/cli/package.json**
   - Add ink-text-input dependency

2. **apps/cli/src/components/edit-entry-modal.tsx** 
   - Import TextInput component
   - Replace manual text input handling with TextInput components
   - Update layout structure for proper form styling
   - Maintain keyboard navigation compatibility

## Benefits
- Professional form appearance
- Better user experience with proper input components
- Reduced complexity in input handling code
- Improved accessibility and keyboard navigation
- Cleaner, more maintainable code structure
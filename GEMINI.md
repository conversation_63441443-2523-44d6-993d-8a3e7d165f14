# GEMINI.md

## Project Overview

This project is a command-line interface (CLI) tool named "tempo-cli" for interacting with Tempo. It is built using TypeScript, React, Ink, and `meow`. The project is structured as a monorepo using pnpm workspaces and turbo for managing the build process.

The main application is located in `apps/cli`. It provides a calendar view to navigate through months and days, and a detailed view for each day, showing time entries. It seems to be a tool for tracking time.

## Building and Running

The project uses `pnpm` as a package manager and `turbo` as a build system.

- **Install dependencies:**
  ```bash
  pnpm install
  ```

- **Build the project:**
  ```bash
  pnpm build
  ```

- **Run the CLI:**
  ```bash
  pnpm start
  ```

- **Run tests:**
  ```bash
  pnpm test
  ```

## Development Conventions

- The project uses `biome` for linting and formatting.
- The code is written in TypeScript.
- The project uses `vitest` for testing.
- The project follows the conventional commits specification for commit messages.
- The project uses `husky` for pre-commit hooks.
- The project uses `auto` for release automation.

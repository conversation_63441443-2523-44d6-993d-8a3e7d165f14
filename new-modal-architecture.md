# New Modal Architecture Design (Pattern 1: Props-Based)

## Overview
Replace string-based state machine with individual boolean states for each modal, allowing each modal to be self-contained while maintaining simple coordination.

## Key Design Principles

1. **Individual Boolean States**: Each modal has its own `show{ModalName}` boolean state
2. **Props-Based Control**: Modals receive `isOpen` prop to control visibility  
3. **Self-Contained Rendering**: Each modal handles its own overlay styling and positioning
4. **Simple Coordination**: App manages modal states to ensure mutual exclusivity

## State Structure

### Current (String-Based)
```typescript
type ViewState = 'main' | 'timeEdit' | 'editEntry';
const [currentView, setCurrentView] = useState<ViewState>('main');
```

### New (Boolean-Based)  
```typescript
const [showTimeEditModal, setShowTimeEditModal] = useState(false);
const [showEditEntryModal, setShowEditEntryModal] = useState(false);
const [editingEntry, setEditingEntry] = useState<TimeEntry | null>(null);
```

## Modal Component Changes

### TimeEditModal
- **Add Props**: `isOpen: boolean`
- **Conditional Rendering**: `if (!isOpen) return null;`
- **Input Handling**: Only process input when `isOpen` is true
- **Self-Contained Overlay**: Handle own centering/overlay styling

### EditEntryModal  
- **Add Props**: `isOpen: boolean`
- **Conditional Rendering**: `if (!isOpen) return null;`
- **Input Handling**: Only process input when `isOpen` is true
- **Fix Styling Conflict**: Remove App's wrapper Box, modal handles own overlay

## App Component Logic

### Handler Functions
```typescript
const handleOpenTimeEditModal = () => {
  setShowTimeEditModal(true);
  // Close other modals
  setShowEditEntryModal(false);
  setEditingEntry(null);
};

const handleOpenEditEntryModal = (entry: TimeEntry) => {
  setEditingEntry(entry);
  setShowEditEntryModal(true);
  // Close other modals
  setShowTimeEditModal(false);
};

const handleCloseTimeEditModal = () => {
  setShowTimeEditModal(false);
};

const handleCloseEditEntryModal = () => {
  setShowEditEntryModal(false);
  setEditingEntry(null);
  // Return to time edit modal if it was open before
  setShowTimeEditModal(true);
};
```

### Rendering Logic
```typescript
return (
  <Box flexDirection="column" width="100%">
    {/* Always render modals - they control their own visibility */}
    <TimeEditModal 
      isOpen={showTimeEditModal}
      day={detail.day}
      entries={detail.entries}
      month={detail.month}
      year={detail.year}
      onClose={handleCloseTimeEditModal}
      onEditEntry={handleOpenEditEntryModal}
    />
    
    <EditEntryModal
      isOpen={showEditEntryModal && !!editingEntry}
      entry={editingEntry}
      onClose={handleCloseEditEntryModal}
      onSave={handleSaveEntry}
    />

    {/* Main content - show when no modals open */}
    {!showTimeEditModal && !showEditEntryModal && (
      <Box flexDirection="row" width="100%">
        <Calendar onOpenTimeEdit={handleOpenTimeEditModal} />
        <DayPane />
      </Box>
    )}
  </Box>
);
```

## Benefits

1. **Self-Contained**: Each modal manages its internal logic independently
2. **Clear State**: Boolean states are more readable than string constants  
3. **Easy Coordination**: Simple boolean logic for mutual exclusivity
4. **Maintainable**: Adding new modals requires minimal changes
5. **Testable**: Easy to test individual modal states

## EditEntryModal Styling Fix

**Problem**: Currently App wraps EditEntryModal in centering Box, but modal already has absolute positioning with black background, causing conflicts.

**Solution**: Remove App's wrapper Box and let EditEntryModal handle its own overlay:

```typescript
// Current - WRONG (double overlay)
{currentView === 'editEntry' && editingEntry && (
  <Box alignItems="center" height="100%" justifyContent="center" width="100%">
    <EditEntryModal entry={editingEntry} />
  </Box>
)}

// New - CORRECT (modal handles own overlay)  
<EditEntryModal
  isOpen={showEditEntryModal && !!editingEntry}
  entry={editingEntry}
/>
```

## Migration Path

1. ✅ Update App component state structure  
2. ✅ Update handler functions
3. ✅ Update modal components to accept `isOpen` prop
4. ✅ Fix EditEntryModal styling conflict
5. ✅ Update rendering logic
6. ✅ Test all modal interactions
7. ✅ Update tests to use new state structure

## Backward Compatibility

This change maintains all existing functionality while improving the architecture:
- Same keyboard shortcuts
- Same visual appearance  
- Same modal interactions
- Same data flow patterns

The only change is internal state management moving from string-based to boolean-based.
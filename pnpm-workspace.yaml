packages:
  - apps/*
  - packages/*

catalog:
  '@biomejs/biome': 2.1.4
  '@tsconfig/strictest': 2.0.5
  '@types/node': 24.2.1
  ava: 6.4.1
  husky: 9.1.7
  lint-staged: 16.1.5
  ts-node: 10.9.2
  tsx: 4.20.3
  turbo: 2.5.5
  typescript: 5.9.2
  ultracite: 5.1.2

catalogs:
  auto:
    '@auto-it/conventional-commits': 11.3.0
    '@auto-it/git-tag': 11.3.0
    '@auto-it/npm': 11.3.0
    '@auto-it/omit-commits': 11.3.0
    '@auto-it/omit-release-notes': 11.3.0
    '@auto-it/pr-body-labels': 11.3.0
    '@auto-it/released': 11.3.0
    auto: 11.3.0
  cli:
    '@inkjs/ui': 2.0.0
    chalk: 5.5.0
    ink: 6.2.0
    ink-testing-library: 4.0.0
    meow: 13.2.0
    react: 19.1.1
  react:
    '@types/react': 19.1.10
    react: 19.1.1
  testing:
    '@testing-library/react': 16.3.0
    '@vitejs/plugin-react': 4.7.0
    '@vitest/coverage-v8': 3.2.4
    jsdom: 26.1.0
    vitest: 3.2.4

onlyBuiltDependencies:
  - esbuild
  - unrs-resolver

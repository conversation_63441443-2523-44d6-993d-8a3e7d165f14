name: Release and Publish

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  release:
    name: Release and Publish CLI Package
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
      pull-requests: write
      issues: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.18.0'
          registry-url: 'https://npm.pkg.github.com'
          scope: '@${{ github.repository_owner }}'

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run linting
        run: pnpm lint

      - name: Run tests
        run: pnpm test

      - name: Build application
        run: pnpm build

      - name: Check build artifacts
        run: |
          if [ ! -f "apps/cli/dist/cli.js" ]; then
            echo "Build failed: apps/cli/dist/cli.js not found"
            exit 1
          fi
          echo "Build successful: apps/cli/dist/cli.js exists"

      - name: Create Auto Release
        id: auto_release
        run: |
          # Configure git for auto
          git config --global user.name "pulsar[bot]"
          git config --global user.email "pulsar[bot]@users.noreply.github.com"

          # Run auto to create release and get version
          pnpm exec auto shipit --dry-run --quiet > auto_output.txt 2>&1 || true

          # Check if there are changes to release
          if grep -q "No new changes" auto_output.txt; then
            echo "No changes to release"
            echo "should_release=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Run auto shipit to create release
          #auto shipit

          # Get the new version
          NEW_VERSION=$(git describe --tags --abbrev=0 | sed 's/^v//')
          echo "version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "should_release=true" >> $GITHUB_OUTPUT
          echo "Released version: $NEW_VERSION"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # - name: Prepare package for publishing
      #   if: steps.auto_release.outputs.should_release == 'true'
      #   run: |
      #     cd apps/cli/dist

      #     # Update package name to include scope for GitHub Packages
      #     npm pkg set name="@${{ github.repository_owner }}/tempo"

      #     # Set the version from auto release
      #     npm pkg set version="${{ steps.auto_release.outputs.version }}"

      #     # Update bin path to be relative to package root
      #     npm pkg set bin="cli.js"

      #     # Remove private flag to allow publishing
      #     npm pkg delete private

      #     # Add repository information
      #     npm pkg set repository.type="git"
      #     npm pkg set repository.url="git+https://github.com/${{ github.repository }}.git"
      #     npm pkg set homepage="https://github.com/${{ github.repository }}#readme"
      #     npm pkg set bugs.url="https://github.com/${{ github.repository }}/issues"

      #     # Add keywords for better discoverability
      #     npm pkg set keywords='["cli", "tui", "terminal", "tempo"]'

      #     echo "Package prepared for publishing:"
      #     cat package.json

      # - name: Publish to GitHub Packages
      #   if: steps.auto_release.outputs.should_release == 'true'
      #   run: |
      #     cd apps/cli/dist
      #     npm publish
      #   env:
      #     NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

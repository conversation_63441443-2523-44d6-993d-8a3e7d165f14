# Self-Contained Component Architecture

## Overview

This document outlines the refactored architecture for the tempo-cli application, which now follows a **self-contained component** pattern. The previous confusing overlapping hook structure has been simplified to make each view/modal completely independent.

## Architecture Principles

### 1. Component Ownership
- Each modal/view manages its own internal state completely
- Components expose simple callbacks for external actions
- No cross-component dependencies or shared state

### 2. Simple App Coordination
- App component only decides which view is currently active
- App doesn't manage how individual views behave internally
- Clean separation between routing logic and business logic

### 3. Callback-Based Communication
- Components communicate through simple callback props
- Events flow upward through callbacks (e.g., `onClose`, `onSave`, `onEditEntry`)
- No complex state synchronization between components

## Component Structure

### App Component (Modal Coordinator)
The [`App`](apps/cli/src/app.tsx) component now uses individual boolean states for each modal:

```typescript
const [showTimeEditModal, setShowTimeEditModal] = useState(false);
const [showEditEntryModal, setShowEditEntryModal] = useState(false);
const [editingEntry, setEditingEntry] = useState<TimeEntry | null>(null);
```

**Responsibilities:**
- Coordinates modal visibility through individual boolean states
- Ensures only one modal is open at a time
- Passes data between modals when needed (e.g., `editingEntry`)
- Renders modals with `isOpen` prop for self-contained control

**Benefits over string-based state machine:**
- Each modal is truly self-contained with conditional rendering
- Boolean states are more readable and maintainable
- Easier to add new modals without modifying existing logic
- Clear separation between modal coordination and internal modal behavior

### Self-Contained Components

#### TimeEditModal
- **Self-Contained Rendering**: Handles own visibility with `isOpen` prop
- **Internal State**: [`selectedIndex`](apps/cli/src/time-edit-modal.tsx:75) for navigation
- **Internal Logic**: Conditional input handling (only when `isOpen`), keyboard navigation
- **External Interface**:
  - Props: `isOpen`, `day`, `month`, `year`, `entries`, `onClose`, `onEditEntry`
  - Callbacks: `onClose()`, `onEditEntry(entry)`
- **Overlay Styling**: Self-contained modal overlay with black background and centering

#### EditEntryModal
- **Self-Contained Rendering**: Handles own visibility with `isOpen` prop
- **Internal State**: Form fields (`ticket`, `title`, `description`, `hours`), focus management
- **Internal Logic**: Conditional input handling (only when `isOpen`), field navigation, form submission
- **External Interface**:
  - Props: `isOpen`, `entry`, `onSave`, `onClose`
  - Callbacks: `onSave(updatedEntry)`, `onClose()`
- **State Synchronization**: Uses `useEffect` to update form fields when `entry` prop changes
- **Overlay Styling**: Self-contained modal overlay with black background and centering

### Simplified Hook Structure

#### useTempoState (Core App State Only)
The [`useTempoState`](apps/cli/src/hooks/use-tempo-state.ts) hook now focuses exclusively on core application state:

```typescript
export type TempoState = {
  // Calendar state only
  month: number;
  year: number;
  detail: DetailData;
  dayData: Record<number, DayData>;
  
  // Core actions only
  actions: {
    handleMonthChange: (newMonth: number, newYear: number) => void;
    handleOpenDay: (day: number, m: number, y: number) => void;
    handleSelectedDayChange: (day: number) => void;
  };
};
```

**Removed from useTempoState:**
- Modal visibility state (`showTimeEditModal`, `showEditEntryModal`)
- Modal-specific actions (`handleOpenTimeEditModal`, etc.)
- Cross-modal coordination logic

#### Removed Hooks
The following hooks were removed as their functionality moved into components:
- ❌ `useModalState` - replaced by App component state
- ❌ `useTimeEditModal` - logic moved into TimeEditModal component
- ❌ `useEditEntry` - logic moved into EditEntryModal component

## Data Flow

```
App (State Machine)
├── Calendar ─────────────┐
├── TimeEditModal         │
└── EditEntryModal        │
                         │
useTempoState ───────────┘
├── useDateNavigation
├── useDaySelection
└── useTimeData
```

**Key Flow Patterns:**
1. **View Switching**: App manages `currentView` state
2. **Data Down**: Props flow down to components
3. **Events Up**: Components call App callbacks to request view changes
4. **Self-Contained Logic**: Each component handles its own internal behavior

## Benefits Achieved

### 1. Easier Testing
- Each component can be tested in complete isolation
- No complex mock setups for interdependent hooks
- Clear component boundaries make unit testing straightforward

### 2. Better Maintainability  
- Changes to one modal don't affect others
- Component responsibilities are obvious and contained
- Less cognitive overhead when working on individual features

### 3. Clearer Mental Model
- Easy to understand: "App decides what to show, components handle how to show it"
- No hidden state dependencies between components
- Linear data flow is easy to trace and debug

### 4. Simpler Debugging
- State is localized to where it's used
- No mysterious cross-component state synchronization issues
- Component-specific problems are isolated and easier to diagnose

### 5. Future Extensibility
- Adding new modals requires minimal changes to existing code
- New components follow the same simple patterns
- App component remains a simple state machine

## Migration Summary

The refactoring preserved all existing behavior while dramatically improving the code structure:

- **Before**: 145 tests passing with complex, overlapping hooks
- **After**: 145 tests still passing with clear, self-contained components
- **Zero functionality changes**: All user-facing behavior identical
- **Architecture improvements**: Better separation of concerns, easier maintenance

## Usage Patterns

### Adding a New Modal
1. Create self-contained component with `isOpen` prop and conditional rendering
2. Add new boolean state to App component (e.g., `showNewModal`)
3. Add handler functions to open/close the modal and coordinate with other modals
4. Component handles all its own internal logic and overlay styling
5. Always render modal in App but let it control its own visibility

**Example:**
```typescript
// In App component
const [showNewModal, setShowNewModal] = useState(false);

const handleOpenNewModal = () => {
  setShowNewModal(true);
  // Close other modals as needed
  setShowTimeEditModal(false);
};

// In JSX
<NewModal
  isOpen={showNewModal}
  onClose={() => setShowNewModal(false)}
  // other props
/>
```

### Component Communication
- Use callback props for parent communication: `onClose`, `onSave`, `onAction`
- Pass data down through props: `entry`, `data`, `config`
- Avoid shared state between sibling components
- Let App coordinate data transfer between views when needed

This architecture makes the codebase much more straightforward and maintainable, with each view being truly self-contained as requested.
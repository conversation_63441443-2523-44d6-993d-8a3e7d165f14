import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render } from 'ink';
import meow from 'meow';

// Mock dependencies
vi.mock('ink', () => ({
  render: vi.fn(),
}));

vi.mock('meow');

vi.mock('../src/app.js', () => ({
  default: () => 'MockApp',
}));

// Mock process.exit to prevent actual exit during tests
const mockProcessExit = vi.spyOn(process, 'exit').mockImplementation(() => {
  throw new Error('process.exit called');
});

describe('CLI Entry Point', () => {
  const mockMeow = vi.mocked(meow);
  const mockRender = vi.mocked(render);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetModules();
  });

  it('should render App component when no flags are provided', async () => {
    // Setup meow mock to return no flags or input
    mockMeow.mockReturnValue({
      flags: {},
      input: [],
      showHelp: vi.fn(),
      showVersion: vi.fn(),
    } as any);

    // Import and run the CLI module
    await import('../src/cli.js');

    expect(mockRender).toHaveBeenCalledOnce();
    expect(mockRender).toHaveBeenCalledWith(expect.anything());
  });

  it('should show help and exit when help flag is provided', async () => {
    const mockShowHelp = vi.fn();
    mockMeow.mockReturnValue({
      flags: { help: true },
      input: [],
      showHelp: mockShowHelp,
      showVersion: vi.fn(),
    } as any);

    await expect(async () => {
      await import('../src/cli.js');
    }).rejects.toThrow('process.exit called');

    expect(mockShowHelp).toHaveBeenCalledOnce();
    expect(mockProcessExit).toHaveBeenCalledWith(0);
    expect(mockRender).not.toHaveBeenCalled();
  });

  it('should show help and exit when input arguments are provided', async () => {
    const mockShowHelp = vi.fn();
    mockMeow.mockReturnValue({
      flags: {},
      input: ['some-argument'],
      showHelp: mockShowHelp,
      showVersion: vi.fn(),
    } as any);

    await expect(async () => {
      await import('../src/cli.js');
    }).rejects.toThrow('process.exit called');

    expect(mockShowHelp).toHaveBeenCalledOnce();
    expect(mockProcessExit).toHaveBeenCalledWith(0);
    expect(mockRender).not.toHaveBeenCalled();
  });

  it('should show version and exit when version flag is provided', async () => {
    const mockShowVersion = vi.fn();
    mockMeow.mockReturnValue({
      flags: { version: true },
      input: [],
      showHelp: vi.fn(),
      showVersion: mockShowVersion,
    } as any);

    await expect(async () => {
      await import('../src/cli.js');
    }).rejects.toThrow('process.exit called');

    expect(mockShowVersion).toHaveBeenCalledOnce();
    expect(mockProcessExit).toHaveBeenCalledWith(0);
    expect(mockRender).not.toHaveBeenCalled();
  });

  it('should configure meow with correct options', async () => {
    mockMeow.mockReturnValue({
      flags: {},
      input: [],
      showHelp: vi.fn(),
      showVersion: vi.fn(),
    } as any);

    await import('../src/cli.js');

    expect(mockMeow).toHaveBeenCalledWith(
      expect.stringContaining('Usage'),
      expect.objectContaining({
        importMeta: expect.any(Object),
        flags: expect.objectContaining({
          help: { type: 'boolean', aliases: ['h'] },
          version: { type: 'boolean', description: 'Print version and exit' },
        }),
      })
    );
  });

  it('should prioritize help over version when both flags are provided', async () => {
    const mockShowHelp = vi.fn();
    const mockShowVersion = vi.fn();
    mockMeow.mockReturnValue({
      flags: { help: true, version: true },
      input: [],
      showHelp: mockShowHelp,
      showVersion: mockShowVersion,
    } as any);

    await expect(async () => {
      await import('../src/cli.js');
    }).rejects.toThrow('process.exit called');

    expect(mockShowHelp).toHaveBeenCalledOnce();
    expect(mockShowVersion).not.toHaveBeenCalled();
    expect(mockProcessExit).toHaveBeenCalledWith(0);
  });
});
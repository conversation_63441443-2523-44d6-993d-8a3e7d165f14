import { render } from 'ink-testing-library';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Calendar from '../../src/calendar.js';

describe('Calendar Navigation', () => {
  const mockOnMonthChange = vi.fn();
  const mockOnEnterDay = vi.fn();
  const mockOnOpenTimeEdit = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render with selected day highlighted when provided via props', () => {
    const { lastFrame } = render(
      <Calendar
        month={8}
        year={2025}
        selectedDay={15}
        onMonthChange={mockOnMonthChange}
        onEnterDay={mockOnEnterDay}
        onOpenTimeEdit={mockOnOpenTimeEdit}
      />
    );

    const output = lastFrame();
    // The calendar should render and the day 15 should be present
    expect(output).toContain('15');
    expect(output).toContain('August 2025');
  });

  it('should render different months correctly with selectedDay prop', () => {
    const { lastFrame } = render(
      <Calendar
        month={9}
        year={2025}
        selectedDay={25}
        onMonthChange={mockOnMonthChange}
        onEnterDay={mockOnEnterDay}
        onOpenTimeEdit={mockOnOpenTimeEdit}
      />
    );

    const output = lastFrame();
    expect(output).toContain('25');
    expect(output).toContain('September 2025');
  });

  it('should render without selectedDay prop', () => {
    const { lastFrame } = render(
      <Calendar
        month={8}
        year={2025}
        onMonthChange={mockOnMonthChange}
        onEnterDay={mockOnEnterDay}
        onOpenTimeEdit={mockOnOpenTimeEdit}
      />
    );

    const output = lastFrame();
    expect(output).toContain('August 2025');
  });

  it('should accept selectedDay prop as optional', () => {
    // Test that the component accepts selectedDay as an optional prop
    const { lastFrame } = render(
      <Calendar
        month={8}
        year={2025}
        selectedDay={undefined}
        onMonthChange={mockOnMonthChange}
        onEnterDay={mockOnEnterDay}
        onOpenTimeEdit={mockOnOpenTimeEdit}
      />
    );

    const output = lastFrame();
    expect(output).toContain('August 2025');
  });
});

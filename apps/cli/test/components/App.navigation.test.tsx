import { render } from 'ink-testing-library';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import App from '../../src/app.js';

// Mock the useInput hook to prevent keyboard input handling during tests
vi.mock('ink', async () => {
  const actual = await vi.importActual('ink');
  return {
    ...actual,
    useInput: vi.fn(),
    useStdout: () => ({
      stdout: {
        columns: 80,
        rows: 24,
      },
    }),
  };
});

// Mock React useState since it's used directly in App
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
  };
});

describe('App Navigation Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the app with calendar and day pane', () => {
    const { lastFrame } = render(<App />);
    const output = lastFrame();
    
    // Should show the calendar with current month
    expect(output).toContain('2025');
    
    // Should show the day pane
    expect(output).toContain('Total:');
    expect(output).toContain('Expected:');
  });

  it('should pass selectedDay prop to Calendar when detail matches current month/year', () => {
    // This test verifies that the App component correctly passes the selectedDay prop
    // to the Calendar component when the detail state matches the current month/year
    const { lastFrame } = render(<App />);
    const output = lastFrame();
    
    // The app should render without errors
    expect(output).toBeDefined();
    expect(output.length).toBeGreaterThan(0);
  });

  it('should handle time edit pane state correctly', () => {
    // This test verifies that the app can handle the showTimeEditModal state
    // which is the core of the navigation issue we're fixing
    const { lastFrame } = render(<App />);
    const output = lastFrame();
    
    // Should show calendar view (not time edit pane) by default
    expect(output).not.toContain('Edit Time Entries');
    expect(output).not.toContain('Press ESC to close');
    
    // Should show calendar and day pane
    expect(output).toContain('Total:');
  });
});

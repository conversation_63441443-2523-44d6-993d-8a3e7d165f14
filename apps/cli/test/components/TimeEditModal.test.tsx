import React from 'react';
import { render } from 'ink-testing-library';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useInput } from 'ink';
import TimeEditModal from '../../src/time-edit-modal.js';
import type { TimeEntry } from '../../src/day-pane.js';

// Mock the useInput hook
vi.mock('ink', async () => {
  const actual = await vi.importActual('ink');
  return {
    ...actual,
    useInput: vi.fn(),
  };
});

describe('TimeEditModal Component', () => {
  const mockOnClose = vi.fn();
  const mockOnEditEntry = vi.fn();
  const mockEntries: TimeEntry[] = [
    { ticket: 'TASK-123', title: 'Test Task 1', description: 'First test task', hours: 2.5 },
    { ticket: 'TASK-456', title: 'Test Task 2', description: 'Second test task', hours: 1.5 },
    { ticket: 'TASK-789', title: 'Test Task 3', description: 'Third test task', hours: 3.0 },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic rendering', () => {
    it('should render the edit pane with correct date and entries', () => {
      const { lastFrame } = render(
        <TimeEditModal
          isOpen={true}
          day={15}
          month={7}
          year={2025}
          entries={mockEntries}
          onClose={mockOnClose}
          onEditEntry={mockOnEditEntry}
        />
      );

      const output = lastFrame();
      expect(output).toContain('Edit Time Entries - July 15, 2025');
      expect(output).toContain('TASK-123 - Test Task 1');
      expect(output).toContain('TASK-456 - Test Task 2');
      expect(output).toContain('TASK-789 - Test Task 3');
      expect(output).toContain('2.5h');
      expect(output).toContain('1.5h');
      expect(output).toContain('3h');
      expect(output).toContain('Use ↑↓ to navigate • ENTER to edit • ESC to close');
    });

    it('should display no entries message when entries array is empty', () => {
      const { lastFrame } = render(
        <TimeEditModal
          isOpen={true}
          day={1}
          month={12}
          year={2025}
          entries={[]}
          onClose={mockOnClose}
          onEditEntry={mockOnEditEntry}
        />
      );

      const output = lastFrame();
      expect(output).toContain('No entries for this day');
      expect(output).toContain('ESC to close');
    });

    it('should display the correct month name', () => {
      const { lastFrame } = render(
        <TimeEditModal
          isOpen={true}
          day={1}
          month={12}
          year={2025}
          entries={mockEntries}
          onClose={mockOnClose}
          onEditEntry={mockOnEditEntry}
        />
      );

      const output = lastFrame();
      expect(output).toContain('December 1, 2025');
    });
  });

  describe('Keyboard navigation', () => {
    it('should call onClose when escape key is pressed', () => {
      render(
        <TimeEditModal
          isOpen={true}
          day={15}
          month={7}
          year={2025}
          entries={mockEntries}
          onClose={mockOnClose}
          onEditEntry={mockOnEditEntry}
        />
      );

      // Simulate ESC key press
      const mockUseInput = vi.mocked(useInput);
      const inputHandler = mockUseInput.mock.calls[0][0];
      inputHandler('', { escape: true });

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should handle up/down arrow key navigation', () => {
      const { lastFrame } = render(
        <TimeEditModal
          isOpen={true}
          day={15}
          month={7}
          year={2025}
          entries={mockEntries}
          onClose={mockOnClose}
          onEditEntry={mockOnEditEntry}
        />
      );

      const mockUseInput = vi.mocked(useInput);
      const inputHandler = mockUseInput.mock.calls[0][0];

      // Initially first item should be selected (backgroundColor: blue)
      expect(lastFrame()).toContain('TASK-123');

      // Navigate down
      inputHandler('', { downArrow: true });
      
      // Navigate up (should stay at 0 since we're at the top)
      inputHandler('', { upArrow: true });
    });

    it('should not call onClose when non-escape keys are pressed', () => {
      render(
        <TimeEditModal
          isOpen={true}
          day={15}
          month={7}
          year={2025}
          entries={mockEntries}
          onClose={mockOnClose}
          onEditEntry={mockOnEditEntry}
        />
      );

      const mockUseInput = vi.mocked(useInput);
      const inputHandler = mockUseInput.mock.calls[0][0];
      
      // Test various keys
      inputHandler('a', { return: false });
      inputHandler('', { downArrow: true });
      inputHandler('', { upArrow: true });
      inputHandler('', { return: true });

      expect(mockOnClose).not.toHaveBeenCalled();
    });
  });

  describe('Entry selection and editing', () => {
    it('should handle enter key press on selected entry', () => {
      const { lastFrame } = render(
        <TimeEditModal
          isOpen={true}
          day={15}
          month={7}
          year={2025}
          entries={mockEntries}
          onClose={mockOnClose}
          onEditEntry={mockOnEditEntry}
        />
      );

      const mockUseInput = vi.mocked(useInput);
      const inputHandler = mockUseInput.mock.calls[0][0];

      // Initial render should show time edit pane
      expect(lastFrame()).toContain('Edit Time Entries - July 15, 2025');
      expect(lastFrame()).toContain('TASK-123 - Test Task 1');

      // Press enter - this should trigger the edit modal state change
      inputHandler('', { return: true });

      // The state change happens internally but the modal rendering is controlled by React state
      // Since we're testing the input handler functionality, we can verify it doesn't crash
      expect(mockOnClose).not.toHaveBeenCalled();
    });
  });

  describe('Entry descriptions', () => {
    it('should display entry descriptions when present', () => {
      const { lastFrame } = render(
        <TimeEditModal
          isOpen={true}
          day={15}
          month={7}
          year={2025}
          entries={mockEntries}
          onClose={mockOnClose}
          onEditEntry={mockOnEditEntry}
        />
      );

      const output = lastFrame();
      expect(output).toContain('First test task');
      expect(output).toContain('Second test task');
      expect(output).toContain('Third test task');
    });

    it('should handle entries without descriptions', () => {
      const entriesWithoutDescriptions: TimeEntry[] = [
        { ticket: 'TASK-100', title: 'No Description Task', description: '', hours: 1.0 },
      ];

      const { lastFrame } = render(
        <TimeEditModal
          isOpen={true}
          day={15}
          month={7}
          year={2025}
          entries={entriesWithoutDescriptions}
          onClose={mockOnClose}
          onEditEntry={mockOnEditEntry}
        />
      );

      const output = lastFrame();
      expect(output).toContain('TASK-100 - No Description Task');
      expect(output).toContain('1h');
    });
  });
});
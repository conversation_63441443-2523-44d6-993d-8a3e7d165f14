import { render } from 'ink-testing-library';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Calendar from '../../src/calendar.js';

// Mock the useInput hook to simulate keyboard input
vi.mock('ink', async () => {
  const actual = await vi.importActual('ink');
  return {
    ...actual,
    useInput: vi.fn(),
  };
});

describe('Calendar Component', () => {
  const mockDayData = {
    1: { entered: 8, expected: 8 },
    2: { entered: 7, expected: 8 },
    3: { entered: 0, expected: 0 }, // Weekend
    4: { entered: 0, expected: 0 }, // Weekend
    5: { entered: 8, expected: 8 },
    15: { entered: 6, expected: 8 },
    31: { entered: 8, expected: 8 },
  };

  const mockOnMonthChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render without crashing', () => {
    const { lastFrame } = render(
      <Calendar
        dayData={mockDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    expect(lastFrame()).toBeDefined();
  });

  it('should display the correct month and year in header', () => {
    const { lastFrame } = render(
      <Calendar
        dayData={mockDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    const output = lastFrame();
    expect(output).toContain('July 2025');
  });

  it('should display day names header', () => {
    const { lastFrame } = render(
      <Calendar
        dayData={mockDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    const output = lastFrame();

    // Check for day names (abbreviated)
    expect(output).toContain('Su');
    expect(output).toContain('Mo');
    expect(output).toContain('Tu');
    expect(output).toContain('We');
    expect(output).toContain('Th');
    expect(output).toContain('Fr');
    expect(output).toContain('Sa');
  });

  it('should display day numbers', () => {
    const { lastFrame } = render(
      <Calendar
        dayData={mockDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    const output = lastFrame();

    // Check for day numbers
    expect(output).toContain('1');
    expect(output).toContain('15');
    expect(output).toContain('31');
  });

  it('should display hour data for days with data', () => {
    const { lastFrame } = render(
      <Calendar
        dayData={mockDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    const output = lastFrame();

    // Check for hour display format
    expect(output).toContain('8h/8h'); // Day 1
    expect(output).toContain('7h/8h'); // Day 2
    expect(output).toContain('6h/8h'); // Day 15
  });

  it('should display difference indicators', () => {
    const { lastFrame } = render(
      <Calendar
        dayData={mockDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    const output = lastFrame();

    // Check for difference indicators
    expect(output).toContain('+0h'); // Day 1 (8-8=0)
    expect(output).toContain('-1h'); // Day 2 (7-8=-1)
    expect(output).toContain('-2h'); // Day 15 (6-8=-2)
  });

  it('should handle different months correctly', () => {
    // Test February (shorter month)
    const { lastFrame } = render(
      <Calendar
        dayData={{}}
        month={2}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    const output = lastFrame();
    expect(output).toContain('February 2025');

    // February 2025 has 28 days, so day 29, 30, 31 should not appear
    expect(output).not.toContain('29');
    expect(output).not.toContain('30');
    expect(output).not.toContain('31');
  });

  it('should handle leap year February correctly', () => {
    const { lastFrame } = render(
      <Calendar
        dayData={{}}
        month={2}
        onMonthChange={mockOnMonthChange}
        year={2024}
      />
    );
    const output = lastFrame();
    expect(output).toContain('February 2024');

    // February 2024 is a leap year, so it should have 29 days
    expect(output).toContain('29');
    expect(output).not.toContain('30');
  });

  it('should work without dayData prop', () => {
    const { lastFrame } = render(
      <Calendar month={7} onMonthChange={mockOnMonthChange} year={2025} />
    );
    const output = lastFrame();
    expect(output).toContain('July 2025');
    expect(output).toContain('0h/0h'); // Default values when no data
  });

  it('should work without onMonthChange prop', () => {
    const { lastFrame } = render(
      <Calendar dayData={mockDayData} month={7} year={2025} />
    );
    const output = lastFrame();
    expect(output).toContain('July 2025');
  });
});

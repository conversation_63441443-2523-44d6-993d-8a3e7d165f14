import { render } from 'ink-testing-library';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Calendar from '../../src/calendar.js';

// Mock the useInput hook
vi.mock('ink', async () => {
  const actual = await vi.importActual('ink');
  return {
    ...actual,
    useInput: vi.fn(),
  };
});

describe('Calendar Data Generation and Display', () => {
  const mockOnMonthChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should display correct calendar layout for July 2025', () => {
    const { lastFrame } = render(
      <Calendar
        dayData={{}}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    const output = lastFrame();

    // July 2025 starts on Tuesday (day 2) and has 31 days
    expect(output).toContain('July 2025');
    expect(output).toContain('31'); // Last day of July
  });

  it('should handle months with different numbers of days', () => {
    // Test February (28 days in non-leap year)
    const { lastFrame: feb2025 } = render(
      <Calendar
        dayData={{}}
        month={2}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    expect(feb2025()).toContain('February 2025');
    expect(feb2025()).not.toContain('29');
    expect(feb2025()).not.toContain('30');
    expect(feb2025()).not.toContain('31');

    // Test February leap year (29 days)
    const { lastFrame: feb2024 } = render(
      <Calendar
        dayData={{}}
        month={2}
        onMonthChange={mockOnMonthChange}
        year={2024}
      />
    );
    expect(feb2024()).toContain('February 2024');
    expect(feb2024()).toContain('29');
    expect(feb2024()).not.toContain('30');

    // Test April (30 days)
    const { lastFrame: apr2025 } = render(
      <Calendar
        dayData={{}}
        month={4}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    expect(apr2025()).toContain('April 2025');
    expect(apr2025()).toContain('30');
    expect(apr2025()).not.toContain('31');
  });

  it('should display hour data correctly', () => {
    const dayData = {
      1: { entered: 8, expected: 8 }, // Perfect day
      2: { entered: 6, expected: 8 }, // Under hours
      3: { entered: 10, expected: 8 }, // Over hours
      4: { entered: 0, expected: 0 }, // Weekend/holiday
      5: { entered: 4, expected: 8 }, // Half day
    };

    const { lastFrame } = render(
      <Calendar
        dayData={dayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    const output = lastFrame();

    // Check hour displays
    expect(output).toContain('8h/8h'); // Day 1
    expect(output).toContain('6h/8h'); // Day 2
    expect(output).toContain('10h/8h'); // Day 3
    expect(output).toContain('0h/0h'); // Day 4
    expect(output).toContain('4h/8h'); // Day 5
  });

  it('should display difference indicators with correct colors', () => {
    const dayData = {
      1: { entered: 8, expected: 8 }, // +0h (neutral)
      2: { entered: 6, expected: 8 }, // -2h (negative)
      3: { entered: 10, expected: 8 }, // +2h (positive)
      4: { entered: 0, expected: 8 }, // -8h (very negative)
      5: { entered: 12, expected: 8 }, // +4h (very positive)
    };

    const { lastFrame } = render(
      <Calendar
        dayData={dayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    const output = lastFrame();

    // Check difference displays
    expect(output).toContain('+0h'); // Day 1
    expect(output).toContain('-2h'); // Day 2
    expect(output).toContain('+2h'); // Day 3
    expect(output).toContain('-8h'); // Day 4
    expect(output).toContain('+4h'); // Day 5
  });

  it('should handle missing day data gracefully', () => {
    const partialDayData = {
      1: { entered: 8, expected: 8 },
      15: { entered: 6, expected: 8 },
      // Missing data for other days
    };

    const { lastFrame } = render(
      <Calendar
        dayData={partialDayData}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    const output = lastFrame();

    // Should show data for days with data
    expect(output).toContain('8h/8h'); // Day 1
    expect(output).toContain('6h/8h'); // Day 15

    // Should show default values for days without data
    expect(output).toContain('0h/0h'); // Default for missing days
  });

  it('should handle empty day data object', () => {
    const { lastFrame } = render(
      <Calendar
        dayData={{}}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    const output = lastFrame();

    // Should show default values for all days
    expect(output).toContain('0h/0h');
    expect(output).toContain('+0h');
  });

  it('should handle undefined day data', () => {
    const { lastFrame } = render(
      <Calendar month={7} onMonthChange={mockOnMonthChange} year={2025} />
    );
    const output = lastFrame();

    // Should render without crashing
    expect(output).toContain('July 2025');
    expect(output).toContain('0h/0h');
  });

  it('should display all month names correctly', () => {
    const months = [
      { num: 1, name: 'January' },
      { num: 2, name: 'February' },
      { num: 3, name: 'March' },
      { num: 4, name: 'April' },
      { num: 5, name: 'May' },
      { num: 6, name: 'June' },
      { num: 7, name: 'July' },
      { num: 8, name: 'August' },
      { num: 9, name: 'September' },
      { num: 10, name: 'October' },
      { num: 11, name: 'November' },
      { num: 12, name: 'December' },
    ];

    for (const { num, name } of months) {
      const { lastFrame } = render(
        <Calendar
          dayData={{}}
          month={num}
          onMonthChange={mockOnMonthChange}
          year={2025}
        />
      );
      expect(lastFrame()).toContain(`${name} 2025`);
    }
  });

  it('should maintain consistent calendar grid structure', () => {
    const { lastFrame } = render(
      <Calendar
        dayData={{}}
        month={7}
        onMonthChange={mockOnMonthChange}
        year={2025}
      />
    );
    const output = lastFrame();

    // Should have day headers (abbreviated)
    const dayHeaders = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];
    for (const day of dayHeaders) {
      expect(output).toContain(day);
    }

    // Should have consistent structure regardless of month length
    expect(output).toContain('July 2025');
  });
});

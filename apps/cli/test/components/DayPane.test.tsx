import React from 'react';
import { render } from 'ink-testing-library';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import DayPane, { type TimeEntry } from '../../src/day-pane.js';

describe('DayPane Component', () => {
  const sampleEntries: TimeEntry[] = [
    {
      ticket: 'ABC-123',
      title: 'Fix bug',
      description: 'Fixed issue',
      hours: 2,
    },
    {
      ticket: 'DEF-456',
      title: 'Add feature',
      description: 'Implemented feature',
      hours: 3,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render entry details and totals', () => {
    const { lastFrame } = render(
      <DayPane
        day={4}
        entries={sampleEntries}
        expected={8}
        month={7}
        year={2025}
      />
    );

    const output = lastFrame();
    expect(output).toContain('July 4, 2025');
    expect(output).toContain('ABC-123 - Fix bug');
    expect(output).toContain('Fixed issue');
    expect(output).toContain('2h');
    expect(output).toContain('Total: 5h');
    expect(output).toContain('Expected: 8h');
    expect(output).toContain('±: -3h');
  });
});

import { describe, it, expect } from 'vitest';

// Helper functions for navigation testing
function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month, 0).getDate();
}



// Simulate left navigation logic
function simulateNavigateLeft(currentYear: number, currentMonth: number, currentDay: number) {
  // Check if we're on the first day of the month
  if (currentDay === 1) {
    let newMonth = currentMonth - 1;
    let newYear = currentYear;

    if (newMonth < 1) {
      newMonth = 12;
      newYear = currentYear - 1;
    }

    const lastDayOfPreviousMonth = getDaysInMonth(newYear, newMonth);
    return { year: newYear, month: newMonth, day: lastDayOfPreviousMonth };
  }

  // Otherwise, just move to previous day
  return { year: currentYear, month: currentMonth, day: currentDay - 1 };
}

// Simulate right navigation logic
function simulateNavigateRight(currentYear: number, currentMonth: number, currentDay: number) {
  const daysInCurrentMonth = getDaysInMonth(currentYear, currentMonth);

  // Check if we're on the last day of the month
  if (currentDay === daysInCurrentMonth) {
    let newMonth = currentMonth + 1;
    let newYear = currentYear;

    if (newMonth > 12) {
      newMonth = 1;
      newYear = currentYear + 1;
    }

    return { year: newYear, month: newMonth, day: 1 };
  }

  // Otherwise, just move to next day
  return { year: currentYear, month: currentMonth, day: currentDay + 1 };
}

describe('Left navigation from first day of month', () => {
  it('should navigate from July 1st to June 30th (last day of June)', () => {
    const result = simulateNavigateLeft(2025, 7, 1);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(6);
    expect(result.day).toBe(30); // June 30, 2025 is the last day of June
  });

  it('should navigate from January 1st to December 31st previous year', () => {
    const result = simulateNavigateLeft(2025, 1, 1);

    expect(result.year).toBe(2024);
    expect(result.month).toBe(12);
    expect(result.day).toBe(31); // December 31, 2024 is the last day of December
  });

  it('should navigate from February 1st to January 31st', () => {
    const result = simulateNavigateLeft(2025, 2, 1);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(1);
    expect(result.day).toBe(31); // January 31, 2025 is the last day of January
  });
});

describe('Right navigation from last day of month', () => {
  it('should navigate from July 31st to August 1st (first day of August)', () => {
    const result = simulateNavigateRight(2025, 7, 31);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(8);
    expect(result.day).toBe(1); // August 1, 2025 is the first day of August
  });

  it('should navigate from December 31st to January 1st next year', () => {
    const result = simulateNavigateRight(2024, 12, 31);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(1);
    expect(result.day).toBe(1); // January 1, 2025 is the first day of January
  });

  it('should navigate from February 28th to March 1st', () => {
    const result = simulateNavigateRight(2025, 2, 28);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(3);
    expect(result.day).toBe(1); // March 1, 2025 is the first day of March
  });

  it('should navigate from February 29th (leap year) to March 1st', () => {
    const result = simulateNavigateRight(2024, 2, 29);

    expect(result.year).toBe(2024);
    expect(result.month).toBe(3);
    expect(result.day).toBe(1); // March 1, 2024 is the first day of March
  });
});

describe('Normal within-month navigation', () => {
  it('should navigate LEFT from July 15th to July 14th', () => {
    const result = simulateNavigateLeft(2025, 7, 15);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(7);
    expect(result.day).toBe(14);
  });

  it('should navigate RIGHT from July 15th to July 16th', () => {
    const result = simulateNavigateRight(2025, 7, 15);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(7);
    expect(result.day).toBe(16);
  });
});

describe('Edge cases with different month lengths', () => {
  it('should navigate LEFT from March 1st to February 28th (non-leap year)', () => {
    const result = simulateNavigateLeft(2025, 3, 1);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(2);
    expect(result.day).toBe(28); // February 28, 2025 is the last day of February (non-leap year)
  });

  it('should navigate RIGHT from April 30th to May 1st', () => {
    const result = simulateNavigateRight(2025, 4, 30);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(5);
    expect(result.day).toBe(1); // May 1, 2025 is the first day of May
  });
  it('should navigate LEFT from March 1st to February 29th (leap year)', () => {
    const result = simulateNavigateLeft(2024, 3, 1);

    expect(result.year).toBe(2024);
    expect(result.month).toBe(2);
    expect(result.day).toBe(29); // February 29, 2024 is the last day of February (leap year)
  });
});

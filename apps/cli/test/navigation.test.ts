import { describe, it, expect } from 'vitest';

// Helper functions extracted from calendar.tsx for testing
function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month, 0).getDate();
}

function findLastDayOfWeekInMonth(
  targetYear: number,
  targetMonth: number,
  dayOfWeek: number
): number {
  const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

  // Start from the last day and work backwards
  for (let day = daysInTargetMonth; day >= 1; day--) {
    const date = new Date(targetYear, targetMonth - 1, day);
    if (date.getDay() === dayOfWeek) {
      return day;
    }
  }

  // Fallback to last day if no match found (shouldn't happen)
  return daysInTargetMonth;
}

function findFirstDayOfWeekInMonth(
  targetYear: number,
  targetMonth: number,
  dayOfWeek: number
): number {
  const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

  // Start from the first day and work forwards
  for (let day = 1; day <= daysInTargetMonth; day++) {
    const date = new Date(targetYear, targetMonth - 1, day);
    if (date.getDay() === dayOfWeek) {
      return day;
    }
  }

  // Fallback to first day if no match found (shouldn't happen)
  return 1;
}

function getCurrentDayOfWeek(
  selectedDay: number,
  year: number,
  month: number
): number {
  const date = new Date(year, month - 1, selectedDay);
  return date.getDay();
}

describe('Core navigation functions', () => {
  it('should find last day of week in month correctly', () => {
    // June 2025: last Sunday should be June 29th
    const lastSunday = findLastDayOfWeekInMonth(2025, 6, 0);
    expect(lastSunday).toBe(29);

    // Verify it's actually a Sunday
    const date = new Date(2025, 5, lastSunday);
    expect(date.getDay()).toBe(0);
  });

  it('should find first day of week in month correctly', () => {
    // August 2025: first Thursday should be August 7th
    const firstThursday = findFirstDayOfWeekInMonth(2025, 8, 4);
    expect(firstThursday).toBe(7);

    // Verify it's actually a Thursday
    const date = new Date(2025, 7, firstThursday);
    expect(date.getDay()).toBe(4);
  });

  it('should get current day of week correctly', () => {
    // July 6, 2025 is a Sunday
    expect(getCurrentDayOfWeek(6, 2025, 7)).toBe(0);

    // July 31, 2025 is a Thursday
    expect(getCurrentDayOfWeek(31, 2025, 7)).toBe(4);
  });

  it('should get days in month correctly', () => {
    expect(getDaysInMonth(2025, 7)).toBe(31); // July
    expect(getDaysInMonth(2025, 2)).toBe(28); // February non-leap
    expect(getDaysInMonth(2024, 2)).toBe(29); // February leap
  });
});

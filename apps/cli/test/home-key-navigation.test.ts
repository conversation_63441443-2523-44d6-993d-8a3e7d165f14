import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useCalendarNavigation } from '../src/hooks/use-calendar-navigation.js';

describe('H Key Navigation (Home alternative)', () => {
  const mockOnMonthChange = vi.fn();
  const mockUpdateSelectedDay = vi.fn();
  const mockGetFirstValidDay = vi.fn(() => 1);

  const createMockHelpers = () => ({
    getDaysInMonth: (year: number, month: number) => new Date(year, month, 0).getDate(),
    getFirstValidDay: mockGetFirstValidDay,
    updateSelectedDay: mockUpdateSelectedDay,
    findDayPosition: vi.fn(() => ({ weekIndex: 1, dayIndex: 3 })),
    findLastValidDayInWeek: vi.fn(() => 7),
    findFirstValidDayInWeek: vi.fn(() => 1),
    findLastDayOfWeekInMonth: vi.fn(() => 31),
    findFirstDayOfWeekInMonth: vi.fn(() => 1),
  });

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock current date to be predictable - January 15, 2025
    vi.setSystemTime(new Date(2025, 0, 15)); // January 15, 2025
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should navigate to today when already in current month', () => {
    const state = {
      weeks: [[1, 2, 3, 4, 5, 6, 7], [8, 9, 10, 11, 12, 13, 14]],
      selectedDay: 10, // Different day in same month
      month: 1, // January (current month)
      year: 2025, // Current year
    };

    const callbacks = {
      onMonthChange: mockOnMonthChange,
      onSelectedDayChange: vi.fn(),
      onEnterDay: vi.fn(),
      onOpenTimeEdit: vi.fn(),
    };

    const { result } = renderHook(() =>
      useCalendarNavigation(state, callbacks, createMockHelpers())
    );

    act(() => {
      // Simulate 'h' key press (Home key alternative)
      result.current.handleKeyboardInput('h', {});
    });

    // Should not change month/year since already in current month
    expect(mockOnMonthChange).not.toHaveBeenCalled();
    // Should update to today's date (15th)
    expect(mockUpdateSelectedDay).toHaveBeenCalledWith(15);
  });

  it('should navigate to today when in different month', () => {
    const state = {
      weeks: [[1, 2, 3, 4, 5, 6, 7], [8, 9, 10, 11, 12, 13, 14]],
      selectedDay: 20,
      month: 8, // August (different from current month)
      year: 2025, // Current year
    };

    const callbacks = {
      onMonthChange: mockOnMonthChange,
      onSelectedDayChange: vi.fn(),
      onEnterDay: vi.fn(),
      onOpenTimeEdit: vi.fn(),
    };

    const { result } = renderHook(() =>
      useCalendarNavigation(state, callbacks, createMockHelpers())
    );

    act(() => {
      // Simulate 'h' key press (Home key alternative)
      result.current.handleKeyboardInput('h', {});
    });

    // Should change to current month/year
    expect(mockOnMonthChange).toHaveBeenCalledWith(1, 2025); // January 2025
    // Should update to today's date (15th)
    expect(mockUpdateSelectedDay).toHaveBeenCalledWith(15);
  });

  it('should navigate to today when in different year', () => {
    const state = {
      weeks: [[1, 2, 3, 4, 5, 6, 7], [8, 9, 10, 11, 12, 13, 14]],
      selectedDay: 5,
      month: 12, // December
      year: 2024, // Different year
    };

    const callbacks = {
      onMonthChange: mockOnMonthChange,
      onSelectedDayChange: vi.fn(),
      onEnterDay: vi.fn(),
      onOpenTimeEdit: vi.fn(),
    };

    const { result } = renderHook(() =>
      useCalendarNavigation(state, callbacks, createMockHelpers())
    );

    act(() => {
      // Simulate 'h' key press (Home key alternative)
      result.current.handleKeyboardInput('h', {});
    });

    // Should change to current month/year
    expect(mockOnMonthChange).toHaveBeenCalledWith(1, 2025); // January 2025
    // Should update to today's date (15th)
    expect(mockUpdateSelectedDay).toHaveBeenCalledWith(15);
  });

  it('should work when no onMonthChange callback is provided', () => {
    const state = {
      weeks: [[1, 2, 3, 4, 5, 6, 7], [8, 9, 10, 11, 12, 13, 14]],
      selectedDay: 10,
      month: 8, // Different month
      year: 2025,
    };

    const callbacks = {
      onMonthChange: undefined, // No callback provided
      onSelectedDayChange: vi.fn(),
      onEnterDay: vi.fn(),
      onOpenTimeEdit: vi.fn(),
    };

    const { result } = renderHook(() =>
      useCalendarNavigation(state, callbacks, createMockHelpers())
    );

    act(() => {
      // Simulate 'h' key press (Home key alternative)
      result.current.handleKeyboardInput('h', {});
    });

    // Should not crash and should not update anything
    expect(mockUpdateSelectedDay).not.toHaveBeenCalled();
  });

  it('should work at different times of day', () => {
    // Test with a different date to ensure it's dynamic
    vi.setSystemTime(new Date(2025, 5, 20, 14, 30, 0)); // June 20, 2025, 2:30 PM

    const state = {
      weeks: [[1, 2, 3, 4, 5, 6, 7], [8, 9, 10, 11, 12, 13, 14]],
      selectedDay: 1,
      month: 1, // January (different from current)
      year: 2025,
    };

    const callbacks = {
      onMonthChange: mockOnMonthChange,
      onSelectedDayChange: vi.fn(),
      onEnterDay: vi.fn(),
      onOpenTimeEdit: vi.fn(),
    };

    const { result } = renderHook(() =>
      useCalendarNavigation(state, callbacks, createMockHelpers())
    );

    act(() => {
      // Simulate 'h' key press (Home key alternative)
      result.current.handleKeyboardInput('h', {});
    });

    // Should change to current month/year (June 2025)
    expect(mockOnMonthChange).toHaveBeenCalledWith(6, 2025); // June 2025
    // Should update to today's date (20th)
    expect(mockUpdateSelectedDay).toHaveBeenCalledWith(20);
  });

  it('should have home command in NavigationCommand type', () => {
    // This is a type check - if the home type was added correctly, this should compile
    const validCommands = ['pageUp', 'pageDown', 'home', 'left', 'right', 'up', 'down', 'enter', 'initialize'];
    
    expect(validCommands).toContain('home');
  });
});
import { describe, it, expect } from 'vitest';

// Helper functions for navigation testing
function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month, 0).getDate();
}

function findLastDayOfWeekInMonth(targetYear: number, targetMonth: number, dayOfWeek: number): number {
  const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

  for (let day = daysInTargetMonth; day >= 1; day--) {
    const date = new Date(targetYear, targetMonth - 1, day);
    if (date.getDay() === dayOfWeek) {
      return day;
    }
  }

  return daysInTargetMonth;
}

function findFirstDayOfWeekInMonth(targetYear: number, targetMonth: number, dayOfWeek: number): number {
  const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

  for (let day = 1; day <= daysInTargetMonth; day++) {
    const date = new Date(targetYear, targetMonth - 1, day);
    if (date.getDay() === dayOfWeek) {
      return day;
    }
  }

  return 1;
}

function getCurrentDayOfWeek(selectedDay: number, year: number, month: number): number {
  const date = new Date(year, month - 1, selectedDay);
  return date.getDay();
}

// Simulate up navigation to previous month
function simulateNavigateUpToPreviousMonth(currentYear: number, currentMonth: number, currentDay: number) {
  let newMonth = currentMonth - 1;
  let newYear = currentYear;

  if (newMonth < 1) {
    newMonth = 12;
    newYear = currentYear - 1;
  }

  const currentDayOfWeek = getCurrentDayOfWeek(currentDay, currentYear, currentMonth);
  const targetDay = findLastDayOfWeekInMonth(newYear, newMonth, currentDayOfWeek);

  return { year: newYear, month: newMonth, day: targetDay };
}

// Simulate down navigation to next month
function simulateNavigateDownToNextMonth(currentYear: number, currentMonth: number, currentDay: number) {
  let newMonth = currentMonth + 1;
  let newYear = currentYear;

  if (newMonth > 12) {
    newMonth = 1;
    newYear = currentYear + 1;
  }

  const currentDayOfWeek = getCurrentDayOfWeek(currentDay, currentYear, currentMonth);
  const targetDay = findFirstDayOfWeekInMonth(newYear, newMonth, currentDayOfWeek);

  return { year: newYear, month: newMonth, day: targetDay };
}

describe('Up navigation scenarios', () => {
  it('should navigate July 6th (Sunday) UP to last Sunday of June', () => {
    const result = simulateNavigateUpToPreviousMonth(2025, 7, 6);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(6);
    expect(result.day).toBe(29); // June 29, 2025 is the last Sunday

    // Verify both days are Sundays
    const originalDayOfWeek = getCurrentDayOfWeek(6, 2025, 7);
    const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
    expect(originalDayOfWeek).toBe(0); // Sunday
    expect(targetDayOfWeek).toBe(0); // Sunday
  });

  it('should navigate July 2nd (Wednesday) UP to last Wednesday of June', () => {
    const result = simulateNavigateUpToPreviousMonth(2025, 7, 2);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(6);
    expect(result.day).toBe(25); // June 25, 2025 is the last Wednesday

    // Verify both days are Wednesdays
    const originalDayOfWeek = getCurrentDayOfWeek(2, 2025, 7);
    const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
    expect(originalDayOfWeek).toBe(3); // Wednesday
    expect(targetDayOfWeek).toBe(3); // Wednesday
  });
});

describe('Down navigation scenarios', () => {
  it('should navigate July 31st (Thursday) DOWN to first Thursday of August', () => {
    const result = simulateNavigateDownToNextMonth(2025, 7, 31);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(8);
    expect(result.day).toBe(7); // August 7, 2025 is the first Thursday

    // Verify both days are Thursdays
    const originalDayOfWeek = getCurrentDayOfWeek(31, 2025, 7);
    const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
    expect(originalDayOfWeek).toBe(4); // Thursday
    expect(targetDayOfWeek).toBe(4); // Thursday
  });

  it('should navigate February 28th (Friday) DOWN to first Friday of March', () => {
    const result = simulateNavigateDownToNextMonth(2025, 2, 28);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(3);
    expect(result.day).toBe(7); // March 7, 2025 is the first Friday

    // Verify both days are Fridays
    const originalDayOfWeek = getCurrentDayOfWeek(28, 2025, 2);
    const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
    expect(originalDayOfWeek).toBe(5); // Friday
    expect(targetDayOfWeek).toBe(5); // Friday
  });
});

describe('Year boundary navigation', () => {
  it('should navigate January 1st (Wednesday) UP to last Wednesday of December previous year', () => {
    const result = simulateNavigateUpToPreviousMonth(2025, 1, 1);

    expect(result.year).toBe(2024);
    expect(result.month).toBe(12);
    expect(result.day).toBe(25); // December 25, 2024 is the last Wednesday

    // Verify both days are Wednesdays
    const originalDayOfWeek = getCurrentDayOfWeek(1, 2025, 1);
    const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
    expect(originalDayOfWeek).toBe(3); // Wednesday
    expect(targetDayOfWeek).toBe(3); // Wednesday
  });

  it('should navigate December 31st (Tuesday) DOWN to first Tuesday of January next year', () => {
    const result = simulateNavigateDownToNextMonth(2024, 12, 31);

    expect(result.year).toBe(2025);
    expect(result.month).toBe(1);
    expect(result.day).toBe(7); // January 7, 2025 is the first Tuesday

    // Verify both days are Tuesdays
    const originalDayOfWeek = getCurrentDayOfWeek(31, 2024, 12);
    const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
    expect(originalDayOfWeek).toBe(2); // Tuesday
    expect(targetDayOfWeek).toBe(2); // Tuesday
  });
});

describe('Leap year scenarios', () => {
  it('should navigate February 29th (Thursday) leap year DOWN to first Thursday of March', () => {
    const result = simulateNavigateDownToNextMonth(2024, 2, 29);

    expect(result.year).toBe(2024);
    expect(result.month).toBe(3);
    expect(result.day).toBe(7); // March 7, 2024 is the first Thursday

    // Verify both days are Thursdays
    const originalDayOfWeek = getCurrentDayOfWeek(29, 2024, 2);
    const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
    expect(originalDayOfWeek).toBe(4); // Thursday
    expect(targetDayOfWeek).toBe(4); // Thursday
  });

  it('should navigate March 1st (Friday) UP to last Friday of February in leap year', () => {
    const result = simulateNavigateUpToPreviousMonth(2024, 3, 1);

    expect(result.year).toBe(2024);
    expect(result.month).toBe(2);
    expect(result.day).toBe(23); // February 23, 2024 is the last Friday

    // Verify both days are Fridays
    const originalDayOfWeek = getCurrentDayOfWeek(1, 2024, 3);
    const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
    expect(originalDayOfWeek).toBe(5); // Friday
    expect(targetDayOfWeek).toBe(5); // Friday
  });
});

describe('All days of the week preservation', () => {
  it('should preserve all days of the week correctly', () => {
    // Test a week in July 2025: July 6-12 (Sunday through Saturday)
    for (let day = 6; day <= 12; day++) {
      const dayOfWeek = getCurrentDayOfWeek(day, 2025, 7);

      // Test UP navigation (to June)
      const upResult = simulateNavigateUpToPreviousMonth(2025, 7, day);
      const upDayOfWeek = getCurrentDayOfWeek(upResult.day, upResult.year, upResult.month);
      expect(upDayOfWeek).toBe(dayOfWeek);

      // Test DOWN navigation (to August)
      const downResult = simulateNavigateDownToNextMonth(2025, 7, day);
      const downDayOfWeek = getCurrentDayOfWeek(downResult.day, downResult.year, downResult.month);
      expect(downDayOfWeek).toBe(dayOfWeek);
    }
  });
});

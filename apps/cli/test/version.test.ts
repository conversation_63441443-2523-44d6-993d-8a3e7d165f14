import { describe, it, expect } from 'vitest';
import { cliVersion } from '../src/version.js';

describe('cliVersion', () => {
  it('should export a valid semantic version string', () => {
    expect(cliVersion).toMatch(/^\d+\.\d+\.\d+/);
  });
  
  it('should be a non-empty string', () => {
    expect(cliVersion).toBeTruthy();
    expect(typeof cliVersion).toBe('string');
  });
  
  it('should contain only valid version characters', () => {
    expect(cliVersion).toMatch(/^[\d\.\-\+\w]+$/);
  });
});
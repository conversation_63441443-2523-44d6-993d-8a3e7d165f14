import React from 'react';
import { render } from 'ink-testing-library';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useInput } from 'ink';
import EditEntryModal from '../../src/components/edit-entry-modal.js';
import type { TimeEntry } from '../../src/day-pane.js';

// Mock the useInput hook
vi.mock('ink', async () => {
  const actual = await vi.importActual('ink');
  return {
    ...actual,
    useInput: vi.fn(),
  };
});

describe('EditEntryModal Integration Tests', () => {
  const mockOnClose = vi.fn();
  const mockOnSave = vi.fn();
  const mockEntry: TimeEntry = {
    ticket: 'TASK-123',
    title: 'Test Task',
    description: 'Test Description',
    hours: 2.5,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Visual Layout Validation', () => {
    it('should render all input fields with consistent full-width layout', () => {
      const { lastFrame } = render(
        <EditEntryModal
          isOpen={true}
          entry={mockEntry}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      );

      const output = lastFrame();
      const lines = output.split('\n');
      
      // Find the lines that contain the input field content
      const ticketFieldLine = lines.find(line => 
        line.includes('Enter ticket number') || line.includes('TASK-123')
      );
      const titleFieldLine = lines.find(line => 
        line.includes('Enter title') && !line.includes('Title:')
      );
      const descriptionFieldLine = lines.find(line => 
        line.includes('Enter description') && !line.includes('Description:')
      );
      const hoursFieldLine = lines.find(line => 
        line.includes('2.5') || (line.includes('0') && !line.includes('to cancel'))
      );

      // All field lines should exist
      expect(ticketFieldLine).toBeDefined();
      expect(titleFieldLine).toBeDefined();
      expect(descriptionFieldLine).toBeDefined();
      expect(hoursFieldLine).toBeDefined();

      // All field lines should have similar structure and length
      // They should all span the full width of the modal content area
      if (ticketFieldLine && titleFieldLine && descriptionFieldLine && hoursFieldLine) {
        // Find the start position of content (after border and padding)
        const contentStartIndex = ticketFieldLine.indexOf('Enter ticket number') || ticketFieldLine.indexOf('TASK-123');
        
        // Check that all fields start at similar positions (indicating consistent layout)
        const titleContentStart = titleFieldLine.indexOf('Enter title') !== -1 ? 
          titleFieldLine.indexOf('Enter title') : titleFieldLine.indexOf('Test Task');
        const descContentStart = descriptionFieldLine.indexOf('Enter description') !== -1 ? 
          descriptionFieldLine.indexOf('Enter description') : descriptionFieldLine.indexOf('Test Description');
        
        // All fields should start at similar horizontal positions (within 3 characters)
        expect(Math.abs(contentStartIndex - titleContentStart)).toBeLessThan(3);
        expect(Math.abs(contentStartIndex - descContentStart)).toBeLessThan(3);
        
        // Check that field backgrounds extend to similar end positions
        // This validates that they have consistent width
        const borderChar = '║';
        const ticketEndIndex = ticketFieldLine.lastIndexOf(borderChar);
        const titleEndIndex = titleFieldLine.lastIndexOf(borderChar);
        const descEndIndex = descriptionFieldLine.lastIndexOf(borderChar);
        const hoursEndIndex = hoursFieldLine.lastIndexOf(borderChar);
        
        // All fields should end at the same position (indicating full width)
        expect(ticketEndIndex).toBe(titleEndIndex);
        expect(titleEndIndex).toBe(descEndIndex);
        expect(descEndIndex).toBe(hoursEndIndex);
      }
    });

    it('should maintain consistent field width across all input fields', () => {
      const { lastFrame } = render(
        <EditEntryModal
          isOpen={true}
          entry={mockEntry}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      );

      const output = lastFrame();
      const lines = output.split('\n');
      
      // Find all lines that contain background colored input fields
      // These should have consistent width
      const inputFieldLines = lines.filter(line => 
        (line.includes('Enter ticket') || line.includes('TASK-123') ||
         line.includes('Enter title') || line.includes('Test Task') ||
         line.includes('Enter description') || line.includes('Test Description') ||
         line.includes('2.5') || line.includes('0')) &&
        !line.includes(':') // Exclude label lines
      );

      expect(inputFieldLines.length).toBeGreaterThan(0);

      // All input field lines should have the same total length
      const firstFieldLength = inputFieldLines[0]?.length || 0;
      inputFieldLines.forEach((line, index) => {
        expect(line.length).toBe(firstFieldLength, 
          `Input field ${index} has inconsistent width: ${line.length} vs ${firstFieldLength}`
        );
      });
    });

    it('should show proper visual distinction between labels and input fields', () => {
      const { lastFrame } = render(
        <EditEntryModal
          isOpen={true}
          entry={mockEntry}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      );

      const output = lastFrame();
      const lines = output.split('\n');
      
      // Find label lines
      const labelLines = lines.filter(line => 
        line.includes('Ticket:') || line.includes('Title:') || 
        line.includes('Description:') || line.includes('Hours:')
      );

      // Find input field lines - look for lines with placeholder text or actual values
      const inputLines = lines.filter(line =>
        line.includes('Enter ticket number') || line.includes('TASK-123') ||
        line.includes('Enter title') || line.includes('Test Task') ||
        line.includes('Enter description') || line.includes('Test Description') ||
        (line.includes('2.5') && !line.includes('Hours:'))
      );

      expect(labelLines.length).toBe(4);
      expect(inputLines.length).toBeGreaterThanOrEqual(3); // At least 3 should be found

      // Labels and input fields should be on separate lines
      labelLines.forEach(labelLine => {
        inputLines.forEach(inputLine => {
          expect(labelLine).not.toBe(inputLine);
        });
      });
    });
  });

  describe('Navigation and Field Focus Validation', () => {
    it('should show focus indicator moving between fields with consistent layout', () => {
      const { lastFrame } = render(
        <EditEntryModal
          isOpen={true}
          entry={mockEntry}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      );

      const mockUseInput = vi.mocked(useInput);
      const inputHandler = mockUseInput.mock.calls[0][0];

      // Initial state - should have focus on first field
      let output = lastFrame();
      expect(output).toContain('│'); // Focus indicator

      // Navigate through all fields and verify consistent layout
      for (let i = 0; i < 4; i++) {
        inputHandler('', { tab: true });
        output = lastFrame();
        
        // Check that layout remains consistent
        const lines = output.split('\n');
        const inputLines = lines.filter(line => 
          line.includes('Enter') || line.includes('TASK-') || 
          line.includes('Test') || line.includes('2.5') || line.includes('0')
        );
        
        // Ensure all input lines still have consistent length
        if (inputLines.length > 1) {
          const firstLength = inputLines[0].length;
          inputLines.forEach(line => {
            expect(line.length).toBe(firstLength, 
              `Field ${i}: Inconsistent width after navigation: ${line}`
            );
          });
        }
      }
    });

    it('should maintain form structure throughout interaction flow', () => {
      const { lastFrame } = render(
        <EditEntryModal
          isOpen={true}
          entry={mockEntry}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      );

      const mockUseInput = vi.mocked(useInput);
      const inputHandler = mockUseInput.mock.calls[0][0];

      // Test various interactions
      const interactions = [
        { key: { tab: true } },
        { key: { downArrow: true } },
        { key: { upArrow: true } },
        { input: 'A', key: {} },
        { key: { backspace: true } },
      ];

      interactions.forEach((interaction, index) => {
        if (interaction.input) {
          inputHandler(interaction.input, interaction.key);
        } else {
          inputHandler('', interaction.key);
        }

        const output = lastFrame();
        
        // Verify form structure is maintained
        expect(output).toContain('Edit Time Entry');
        expect(output).toContain('Ticket:');
        expect(output).toContain('Title:');
        expect(output).toContain('Description:');
        expect(output).toContain('Hours:');
        expect(output).toContain('Save');
        
        // Verify input field consistency after each interaction
        const lines = output.split('\n');
        const borderChar = '║';
        const borderedLines = lines.filter(line => line.includes(borderChar));
        
        if (borderedLines.length > 2) {
          const modalWidth = borderedLines[0].length;
          borderedLines.forEach(line => {
            expect(line.length).toBe(modalWidth, 
              `Interaction ${index}: Modal width inconsistency: ${line}`
            );
          });
        }
      });
    });
  });

  describe('Data Display Validation', () => {
    it('should display form data correctly within consistent field widths', () => {
      const testEntry: TimeEntry = {
        ticket: 'A',
        title: 'B',
        description: 'C',
        hours: 1,
      };

      const { lastFrame } = render(
        <EditEntryModal
          isOpen={true}
          entry={testEntry}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      );

      const output = lastFrame();
      const lines = output.split('\n');
      
      // Find field content lines
      const fieldContentLines = lines.filter(line => 
        line.includes('A') || line.includes('B') || line.includes('C') || 
        (line.includes('1') && !line.includes('100%'))
      );

      // Even with minimal content, fields should maintain full width
      if (fieldContentLines.length > 0) {
        const firstFieldWidth = fieldContentLines[0].length;
        fieldContentLines.forEach((line, index) => {
          expect(line.length).toBe(firstFieldWidth, 
            `Short content field ${index} should maintain full width: ${line}`
          );
        });
      }
    });

    it('should handle long content consistently across all fields', () => {
      const longEntry: TimeEntry = {
        ticket: 'VERY-LONG-TICKET-NUMBER-THAT-MIGHT-OVERFLOW',
        title: 'This is a very long title that might cause layout issues',
        description: 'This is an extremely long description that could potentially break the layout if not handled properly',
        hours: 123.45,
      };

      const { lastFrame } = render(
        <EditEntryModal
          isOpen={true}
          entry={longEntry}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      );

      const output = lastFrame();
      const lines = output.split('\n');
      
      // Even with long content, the overall modal structure should be consistent
      const borderChar = '║';
      const borderedLines = lines.filter(line => line.includes(borderChar));
      
      if (borderedLines.length > 2) {
        const modalWidth = borderedLines[0].length;
        borderedLines.forEach((line, index) => {
          expect(line.length).toBe(modalWidth, 
            `Long content line ${index} breaks modal width: ${line.length} vs ${modalWidth}`
          );
        });
      }
    });
  });
});
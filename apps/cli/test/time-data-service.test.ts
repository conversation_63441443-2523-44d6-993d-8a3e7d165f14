import { describe, it, expect, beforeEach, vi } from 'vitest';
import { timeDataService } from '../src/services/time-data-service.js';

describe('TimeDataService', () => {
  beforeEach(() => {
    // Reset any mocked random values
    vi.clearAllMocks();
  });

  describe('generateEntries', () => {
    it('should return empty array for zero total hours', () => {
      expect(timeDataService.generateEntries(0)).toEqual([]);
    });

    it('should return empty array for negative total hours', () => {
      expect(timeDataService.generateEntries(-5)).toEqual([]);
    });

    it('should generate entries that sum to total hours', () => {
      const totalHours = 8;
      const entries = timeDataService.generateEntries(totalHours);
      const summedHours = entries.reduce((sum, entry) => sum + entry.hours, 0);
      expect(summedHours).toBe(totalHours);
    });

    it('should generate at most 3 entries for any total', () => {
      const entries = timeDataService.generateEntries(10);
      expect(entries.length).toBeLessThanOrEqual(3);
      expect(entries.length).toBeGreaterThan(0);
    });

    it('should generate entries with valid ticket format', () => {
      const entries = timeDataService.generateEntries(4);
      entries.forEach(entry => {
        expect(entry.ticket).toMatch(/^[A-Z]{3}-\d{3}$/);
      });
    });

    it('should generate entries with non-empty titles and descriptions', () => {
      const entries = timeDataService.generateEntries(4);
      entries.forEach(entry => {
        expect(entry.title).toBeTruthy();
        expect(entry.description).toBeTruthy();
        expect(typeof entry.title).toBe('string');
        expect(typeof entry.description).toBe('string');
      });
    });

    it('should generate positive hour values', () => {
      const entries = timeDataService.generateEntries(8);
      entries.forEach(entry => {
        expect(entry.hours).toBeGreaterThan(0);
      });
    });
  });

  describe('generateDayData', () => {
    it('should set weekends to 0 expected and entered hours', () => {
      const dayData = timeDataService.generateDayData(2025, 1);
      
      // Check all days in January 2025
      for (let day = 1; day <= 31; day++) {
        const date = new Date(2025, 0, day);
        const weekday = date.getDay();
        
        if (weekday === 0 || weekday === 6) { // Sunday or Saturday
          expect(dayData[day]?.expected).toBe(0);
          expect(dayData[day]?.entered).toBe(0);
        }
      }
    });

    it('should set weekdays to 8 expected hours', () => {
      const dayData = timeDataService.generateDayData(2025, 1);
      
      for (let day = 1; day <= 31; day++) {
        const date = new Date(2025, 0, day);
        const weekday = date.getDay();
        
        if (weekday !== 0 && weekday !== 6) { // Not weekend
          expect(dayData[day]?.expected).toBe(8);
        }
      }
    });

    it('should generate entered hours between 0-8 for weekdays', () => {
      const dayData = timeDataService.generateDayData(2025, 1);
      
      for (let day = 1; day <= 31; day++) {
        const date = new Date(2025, 0, day);
        const weekday = date.getDay();
        
        if (weekday !== 0 && weekday !== 6) { // Not weekend
          expect(dayData[day]?.entered).toBeGreaterThanOrEqual(0);
          expect(dayData[day]?.entered).toBeLessThan(9);
        }
      }
    });

    it('should generate data for all days in month', () => {
      const dayData = timeDataService.generateDayData(2025, 2); // February
      const daysInFeb = new Date(2025, 2, 0).getDate(); // 28 days in 2025
      
      expect(Object.keys(dayData)).toHaveLength(daysInFeb);
      
      for (let day = 1; day <= daysInFeb; day++) {
        expect(dayData[day]).toBeDefined();
        expect(dayData[day]).toHaveProperty('entered');
        expect(dayData[day]).toHaveProperty('expected');
      }
    });
  });

  describe('getFirstValidDay', () => {
    it('should return first weekday in month', () => {
      const firstValidDay = timeDataService.getFirstValidDay(2025, 1);
      expect(firstValidDay).toBeGreaterThan(0);
      expect(firstValidDay).toBeLessThanOrEqual(31);
      
      const date = new Date(2025, 0, firstValidDay);
      const weekday = date.getDay();
      expect(weekday).not.toBe(0); // Not Sunday
      expect(weekday).not.toBe(6); // Not Saturday
    });

    it('should return valid day for different months', () => {
      const months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
      
      months.forEach(month => {
        const firstValidDay = timeDataService.getFirstValidDay(2025, month);
        const daysInMonth = new Date(2025, month, 0).getDate();
        
        expect(firstValidDay).toBeGreaterThan(0);
        expect(firstValidDay).toBeLessThanOrEqual(daysInMonth);
        
        const date = new Date(2025, month - 1, firstValidDay);
        const weekday = date.getDay();
        expect(weekday).not.toBe(0);
        expect(weekday).not.toBe(6);
      });
    });

    it('should return 1 as fallback if no weekdays found', () => {
      // This is unlikely in real scenarios, but tests the fallback
      const result = timeDataService.getFirstValidDay(2025, 1);
      expect(typeof result).toBe('number');
      expect(result).toBeGreaterThanOrEqual(1);
    });
  });
});
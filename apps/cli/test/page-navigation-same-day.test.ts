import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useCalendarNavigation } from '../src/hooks/use-calendar-navigation.js';

describe('Page Navigation Same Day Behavior', () => {
  const mockOnMonthChange = vi.fn();
  const mockUpdateSelectedDay = vi.fn();
  const mockGetFirstValidDay = vi.fn(() => 1);

  const createMockHelpers = () => ({
    getDaysInMonth: (year: number, month: number) => new Date(year, month, 0).getDate(),
    getFirstValidDay: mockGetFirstValidDay,
    updateSelectedDay: mockUpdateSelectedDay,
    findDayPosition: vi.fn(() => ({ weekIndex: 1, dayIndex: 3 })),
    findLastValidDayInWeek: vi.fn(() => 7),
    findFirstValidDayInWeek: vi.fn(() => 1),
    findLastDayOfWeekInMonth: vi.fn(() => 31),
    findFirstDayOfWeekInMonth: vi.fn(() => 1),
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should navigate to same day in previous month when day exists', () => {
    const state = {
      weeks: [[1, 2, 3, 4, 5, 6, 7], [8, 9, 10, 11, 12, 13, 14]],
      selectedDay: 15,
      month: 8, // August
      year: 2025,
    };

    const callbacks = {
      onMonthChange: mockOnMonthChange,
      onSelectedDayChange: vi.fn(),
      onEnterDay: vi.fn(),
      onOpenTimeEdit: vi.fn(),
    };

    const { result } = renderHook(() =>
      useCalendarNavigation(state, callbacks, createMockHelpers())
    );

    act(() => {
      // Simulate Page Up key press
      result.current.handleKeyboardInput('', { pageUp: true });
    });

    expect(mockOnMonthChange).toHaveBeenCalledWith(7, 2025); // July 2025
    expect(mockUpdateSelectedDay).toHaveBeenCalledWith(15); // Same day (15th)
  });

  it('should navigate to same day in next month when day exists', () => {
    const state = {
      weeks: [[1, 2, 3, 4, 5, 6, 7], [8, 9, 10, 11, 12, 13, 14]],
      selectedDay: 6,
      month: 8, // August
      year: 2025,
    };

    const callbacks = {
      onMonthChange: mockOnMonthChange,
      onSelectedDayChange: vi.fn(),
      onEnterDay: vi.fn(),
      onOpenTimeEdit: vi.fn(),
    };

    const { result } = renderHook(() =>
      useCalendarNavigation(state, callbacks, createMockHelpers())
    );

    act(() => {
      // Simulate Page Down key press
      result.current.handleKeyboardInput('', { pageDown: true });
    });

    expect(mockOnMonthChange).toHaveBeenCalledWith(9, 2025); // September 2025
    expect(mockUpdateSelectedDay).toHaveBeenCalledWith(6); // Same day (6th)
  });

  it('should adjust day when navigating from month with more days to month with fewer days', () => {
    const state = {
      weeks: [[1, 2, 3, 4, 5, 6, 7], [8, 9, 10, 11, 12, 13, 14]],
      selectedDay: 31, // January 31st
      month: 1, // January
      year: 2025,
    };

    const callbacks = {
      onMonthChange: mockOnMonthChange,
      onSelectedDayChange: vi.fn(),
      onEnterDay: vi.fn(),
      onOpenTimeEdit: vi.fn(),
    };

    const { result } = renderHook(() =>
      useCalendarNavigation(state, callbacks, createMockHelpers())
    );

    act(() => {
      // Simulate Page Down key press (January -> February)
      result.current.handleKeyboardInput('', { pageDown: true });
    });

    expect(mockOnMonthChange).toHaveBeenCalledWith(2, 2025); // February 2025
    expect(mockUpdateSelectedDay).toHaveBeenCalledWith(28); // Adjusted to Feb 28th (non-leap year)
  });

  it('should navigate across year boundary with Page Up', () => {
    const state = {
      weeks: [[1, 2, 3, 4, 5, 6, 7], [8, 9, 10, 11, 12, 13, 14]],
      selectedDay: 15,
      month: 1, // January
      year: 2025,
    };

    const callbacks = {
      onMonthChange: mockOnMonthChange,
      onSelectedDayChange: vi.fn(),
      onEnterDay: vi.fn(),
      onOpenTimeEdit: vi.fn(),
    };

    const { result } = renderHook(() =>
      useCalendarNavigation(state, callbacks, createMockHelpers())
    );

    act(() => {
      // Simulate Page Up key press (January 2025 -> December 2024)
      result.current.handleKeyboardInput('', { pageUp: true });
    });

    expect(mockOnMonthChange).toHaveBeenCalledWith(12, 2024); // December 2024
    expect(mockUpdateSelectedDay).toHaveBeenCalledWith(15); // Same day (15th)
  });

  it('should navigate across year boundary with Page Down', () => {
    const state = {
      weeks: [[1, 2, 3, 4, 5, 6, 7], [8, 9, 10, 11, 12, 13, 14]],
      selectedDay: 20,
      month: 12, // December
      year: 2024,
    };

    const callbacks = {
      onMonthChange: mockOnMonthChange,
      onSelectedDayChange: vi.fn(),
      onEnterDay: vi.fn(),
      onOpenTimeEdit: vi.fn(),
    };

    const { result } = renderHook(() =>
      useCalendarNavigation(state, callbacks, createMockHelpers())
    );

    act(() => {
      // Simulate Page Down key press (December 2024 -> January 2025)
      result.current.handleKeyboardInput('', { pageDown: true });
    });

    expect(mockOnMonthChange).toHaveBeenCalledWith(1, 2025); // January 2025
    expect(mockUpdateSelectedDay).toHaveBeenCalledWith(20); // Same day (20th)
  });

  it('should default to first valid day when no day is selected', () => {
    const state = {
      weeks: [[1, 2, 3, 4, 5, 6, 7], [8, 9, 10, 11, 12, 13, 14]],
      selectedDay: null, // No day selected
      month: 8, // August
      year: 2025,
    };

    const callbacks = {
      onMonthChange: mockOnMonthChange,
      onSelectedDayChange: vi.fn(),
      onEnterDay: vi.fn(),
      onOpenTimeEdit: vi.fn(),
    };

    const { result } = renderHook(() =>
      useCalendarNavigation(state, callbacks, createMockHelpers())
    );

    act(() => {
      // Simulate Page Up key press
      result.current.handleKeyboardInput('', { pageUp: true });
    });

    expect(mockOnMonthChange).toHaveBeenCalledWith(7, 2025); // July 2025
    expect(mockGetFirstValidDay).toHaveBeenCalled(); // Should call getFirstValidDay
    expect(mockUpdateSelectedDay).toHaveBeenCalledWith(1); // Default to first valid day
  });
});
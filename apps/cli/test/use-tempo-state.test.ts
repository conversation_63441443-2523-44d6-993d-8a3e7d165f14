import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useTempoState } from '../src/hooks/use-tempo-state.js';

// Mock the time data service
vi.mock('../src/services/time-data-service.js', () => ({
  timeDataService: {
    generateDayData: vi.fn(() => ({
      1: { entered: 4, expected: 8 },
      2: { entered: 6, expected: 8 },
      15: { entered: 0, expected: 0 }, // weekend
    })),
    getFirstValidDay: vi.fn(() => 1),
    generateEntries: vi.fn((hours) => 
      hours > 0 
        ? [{ ticket: 'ABC-123', title: 'Test task', description: 'Test desc', hours }]
        : []
    ),
  },
}));

describe('useTempoState', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock current date to be predictable
    vi.setSystemTime(new Date(2025, 0, 15)); // January 15, 2025
  });

  it('should initialize with current month and year', () => {
    const { result } = renderHook(() => useTempoState());
    
    expect(result.current.month).toBe(1); // January (getMonth() + 1)
    expect(result.current.year).toBe(2025);
  });

  it('should initialize detail with current day data', () => {
    const { result } = renderHook(() => useTempoState());
    
    expect(result.current.detail.day).toBe(15);
    expect(result.current.detail.month).toBe(1);
    expect(result.current.detail.year).toBe(2025);
    expect(result.current.detail.expected).toBe(0); // weekend in mock
    expect(result.current.detail.entries).toEqual([]);
  });

  it('should handle month changes correctly', () => {
    const { result } = renderHook(() => useTempoState());
    
    act(() => {
      result.current.actions.handleMonthChange(6, 2024);
    });
    
    expect(result.current.month).toBe(6);
    expect(result.current.year).toBe(2024);
    expect(result.current.detail.month).toBe(6);
    expect(result.current.detail.year).toBe(2024);
  });

  it('should generate day data correctly', () => {
    const { result } = renderHook(() => useTempoState());
    
    expect(result.current.dayData).toBeDefined();
    expect(typeof result.current.dayData).toBe('object');
    
    // Should have mocked data for specific days
    expect(result.current.dayData[1]).toEqual({ entered: 4, expected: 8 });
    expect(result.current.dayData[2]).toEqual({ entered: 6, expected: 8 });
  });

  it('should handle opening a specific day', () => {
    const { result } = renderHook(() => useTempoState());
    
    act(() => {
      result.current.actions.handleOpenDay(5, 3, 2024);
    });
    
    expect(result.current.detail.day).toBe(5);
    expect(result.current.detail.month).toBe(3);
    expect(result.current.detail.year).toBe(2024);
  });

  it('should handle selected day changes within same month/year', () => {
    const { result } = renderHook(() => useTempoState());
    
    act(() => {
      result.current.actions.handleSelectedDayChange(2);
    });
    
    expect(result.current.detail.day).toBe(2);
    expect(result.current.detail.month).toBe(1); // should remain same
    expect(result.current.detail.year).toBe(2025); // should remain same
    expect(result.current.detail.expected).toBe(8); // weekday in mock
  });

  it('should generate day data for current month/year', () => {
    const { result } = renderHook(() => useTempoState());
    
    expect(result.current.dayData).toBeDefined();
    expect(typeof result.current.dayData).toBe('object');
  });

  it('should provide all required actions', () => {
    const { result } = renderHook(() => useTempoState());
    
    expect(result.current.actions).toHaveProperty('handleMonthChange');
    expect(result.current.actions).toHaveProperty('handleOpenDay');
    expect(result.current.actions).toHaveProperty('handleSelectedDayChange');
    
    // Verify they are functions
    expect(typeof result.current.actions.handleMonthChange).toBe('function');
    expect(typeof result.current.actions.handleOpenDay).toBe('function');
    expect(typeof result.current.actions.handleSelectedDayChange).toBe('function');
  });

  it('should have stable action callback functions', () => {
    const { result } = renderHook(() => useTempoState());
    
    // Verify that actions are functions and stable within a single render
    const actions = result.current.actions;
    expect(typeof actions.handleMonthChange).toBe('function');
    expect(typeof actions.handleOpenDay).toBe('function');
    expect(typeof actions.handleSelectedDayChange).toBe('function');
    
    // Verify actions object remains consistent
    expect(result.current.actions).toBe(actions);
  });
});
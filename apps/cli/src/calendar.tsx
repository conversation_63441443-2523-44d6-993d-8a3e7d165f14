import { Box, Text, useInput } from 'ink';
import { useCallback, useEffect, useState } from 'react';
import { useCalendarNavigation } from './hooks/use-calendar-navigation.js';

type DayData = {
  readonly entered: number;
  readonly expected: number;
};

export type CalendarProps = {
  readonly month: number; // 1-12
  readonly year: number;
  readonly dayData?: Record<number, DayData>;
  readonly selectedDay?: number; // The day that should be selected
  readonly onMonthChange?: (month: number, year: number) => void;
  readonly onEnterDay?: (day: number, month: number, year: number) => void;
  readonly onSelectedDayChange?: (day: number) => void; // New callback for day selection changes
  readonly onOpenTimeEdit?: () => void;
};

type DayCellProps = {
  readonly day: number | undefined;
  readonly dayIndex: number;
  readonly month: number;
  readonly year: number;
  readonly dayData?: Record<number, DayData> | undefined;
  readonly isSelected: boolean;
};

const daysInWeek = 7;
const dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];
export const monthNames = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month, 0).getDate();
}

function getFirstDayOfMonth(year: number, month: number): number {
  return new Date(year, month - 1, 1).getDay();
}

function isToday(
  day: number | undefined,
  month: number,
  year: number
): boolean {
  if (day === undefined) {
    return false;
  }

  const today = new Date();
  return (
    today.getDate() === day &&
    today.getMonth() + 1 === month &&
    today.getFullYear() === year
  );
}

function getDiffColor(diff: number): 'green' | 'red' | 'white' {
  if (diff > 0) {
    return 'green';
  }
  if (diff < 0) {
    return 'red';
  }
  return 'white';
}

function getBorderColor(isSelected: boolean, isTodayFlag: boolean): string {
  if (isSelected) {
    return 'cyan';
  }
  if (isTodayFlag) {
    return 'yellow';
  }
  return 'gray';
}

function DayCell({
  day,
  dayIndex,
  month,
  year,
  dayData,
  isSelected,
}: DayCellProps) {
  const isCurrentMonth = day !== undefined;
  const isTodayFlag = isToday(day, month, year);

  const info = isCurrentMonth && dayData ? dayData[day] : undefined;
  const entered = info ? info.entered : 0;
  const expected = info ? info.expected : 0;
  const diff = entered - expected;

  const diffColor = getDiffColor(diff);
  const borderColor = getBorderColor(isSelected, isTodayFlag);

  return (
    <Box
      borderColor={borderColor}
      borderStyle="single"
      flexDirection="column"
      gap={0}
      height={7}
      key={String(day ?? `blank-${dayIndex}`)}
      margin={0}
      padding={0}
      width="14.28%"
    >
      <Box justifyContent="flex-end" width="100%">
        <Text
          bold={isTodayFlag || isSelected}
          color={isCurrentMonth ? 'white' : 'gray'}
        >
          {day ?? ''}
        </Text>
      </Box>
      {isCurrentMonth ? (
        <Box alignItems="center" flexDirection="column">
          <Text>
            {entered}h/{expected}h
          </Text>
          <Text color={diffColor}>
            {diff >= 0 ? '+' : ''}
            {diff}h
          </Text>
        </Box>
      ) : null}
    </Box>
  );
}

function generateCalendarDays(
  year: number,
  month: number
): Array<number | undefined> {
  const daysInMonth = getDaysInMonth(year, month);
  const firstDay = getFirstDayOfMonth(year, month);
  const days: Array<number | undefined> = [];

  // Add empty slots for days before the 1st of the month
  for (let i = 0; i < firstDay; i++) {
    days.push(undefined);
  }

  // Add all days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    days.push(day);
  }

  return days;
}

function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }

  return chunks;
}

export default function Calendar({
  month,
  year,
  dayData,
  selectedDay: propSelectedDay,
  onMonthChange,
  onEnterDay,
  onSelectedDayChange,
  onOpenTimeEdit,
}: CalendarProps) {
  const days = generateCalendarDays(year, month);
  const weeks = chunkArray(days, daysInWeek);
  const monthName = monthNames[month - 1];

  // Ensure we have 6 weeks to maintain consistent height
  while (weeks.length < 5) {
    weeks.push(new Array(daysInWeek).fill(undefined));
  }

  // Use internal state when no callback is provided, otherwise use controlled mode
  const [internalSelectedDay, setInternalSelectedDay] = useState<number | null>(
    null
  );

  // Determine the actual selected day
  const selectedDay = propSelectedDay ?? internalSelectedDay;

  // Helper function to get the first valid day in the month
  const getFirstValidDay = useCallback(() => {
    return days.find((day) => day !== undefined) || 1;
  }, [days]);

  // Initialize internal selected day when no prop is provided and user interacts
  // Don't auto-initialize to allow tests to work properly

  // Helper function to update selected day (either via callback or internal state)
  const updateSelectedDay = useCallback(
    (day: number) => {
      if (onSelectedDayChange) {
        onSelectedDayChange(day);
      } else {
        setInternalSelectedDay(day);
      }
    },
    [onSelectedDayChange]
  );

  // Reset selected day when month changes (but preserve if possible)
  useEffect(() => {
    if (selectedDay !== null) {
      const daysInCurrentMonth = getDaysInMonth(year, month);
      if (selectedDay > daysInCurrentMonth) {
        // If selected day doesn't exist in new month, select the last day
        updateSelectedDay(daysInCurrentMonth);
      }
      // Otherwise keep the same day number if it exists in the new month
    }
  }, [month, year, selectedDay, updateSelectedDay]);

  // Update day details when selected day changes
  useEffect(() => {
    if (onEnterDay && selectedDay !== null) {
      onEnterDay(selectedDay, month, year);
    }
  }, [selectedDay, month, year, onEnterDay]);

  // Helper function to find day position in the grid
  const findDayPosition = (targetDay: number) => {
    for (let weekIndex = 0; weekIndex < weeks.length; weekIndex++) {
      const week = weeks[weekIndex];
      if (week) {
        const dayIndex = week.indexOf(targetDay);
        if (dayIndex !== -1) {
          return { weekIndex, dayIndex };
        }
      }
    }
    return null;
  };

  // Helper functions for finding valid days
  const findLastValidDayInWeek = (
    week: ReadonlyArray<number | undefined>
  ): number | undefined => {
    for (let i = daysInWeek - 1; i >= 0; i--) {
      if (week[i] !== undefined) {
        return week[i];
      }
    }
    return;
  };

  const findFirstValidDayInWeek = (
    week: ReadonlyArray<number | undefined>
  ): number | undefined => {
    for (let i = 0; i < daysInWeek; i++) {
      if (week[i] !== undefined) {
        return week[i];
      }
    }
    return;
  };

  // Helper function to find the last occurrence of a day of week in a month
  const findLastDayOfWeekInMonth = useCallback(
    (targetYear: number, targetMonth: number, dayOfWeek: number): number => {
      const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

      // Start from the last day and work backwards
      for (let day = daysInTargetMonth; day >= 1; day--) {
        const date = new Date(targetYear, targetMonth - 1, day);
        if (date.getDay() === dayOfWeek) {
          return day;
        }
      }

      // Fallback to last day if no match found (shouldn't happen)
      return daysInTargetMonth;
    },
    []
  );

  // Helper function to find the first occurrence of a day of week in a month
  const findFirstDayOfWeekInMonth = useCallback(
    (targetYear: number, targetMonth: number, dayOfWeek: number): number => {
      const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

      // Start from the first day and work forwards
      for (let day = 1; day <= daysInTargetMonth; day++) {
        const date = new Date(targetYear, targetMonth - 1, day);
        if (date.getDay() === dayOfWeek) {
          return day;
        }
      }

      // Fallback to first day if no match found (shouldn't happen)
      return 1;
    },
    []
  );

  // Month navigation functions moved to useCalendarNavigation hook

  // Initialize calendar navigation hook with extracted logic
  const navigationState = {
    weeks,
    selectedDay,
    month,
    year,
  };

  const navigationCallbacks = {
    onMonthChange,
    onSelectedDayChange,
    onEnterDay,
    onOpenTimeEdit,
  };

  const navigationHelpers = {
    getDaysInMonth,
    getFirstValidDay,
    updateSelectedDay,
    findDayPosition,
    findLastValidDayInWeek,
    findFirstValidDayInWeek,
    findLastDayOfWeekInMonth,
    findFirstDayOfWeekInMonth,
  };

  const { handleKeyboardInput } = useCalendarNavigation(
    navigationState,
    navigationCallbacks,
    navigationHelpers
  );

  // Simple keyboard navigation handler - complexity reduced from 15+ to <8
  useInput(handleKeyboardInput);

  return (
    <Box flexDirection="column" gap={0} margin={0} padding={0} width="100%">
      {/* Month and Year Header */}
      <Box
        borderColor="red"
        borderStyle="single"
        justifyContent="center"
        marginBottom={1}
      >
        <Text bold color="cyan">
          {monthName} {year}
        </Text>
      </Box>

      {/* Calendar Grid Container */}
      <Box
        borderColor="gray"
        borderStyle="single"
        flexDirection="column"
        width="100%"
      >
        {/* Day Names Header */}
        <Box flexDirection="row" gap={0} margin={0} padding={0} width="100%">
          {dayNames.map((day) => (
            <Box
              alignItems="center"
              borderColor="gray"
              borderStyle="single"
              gap={0}
              height={3}
              justifyContent="center"
              key={day}
              margin={0}
              padding={0}
              width="14.28%"
            >
              <Text bold>{day}</Text>
            </Box>
          ))}
        </Box>

        {/* Calendar Rows */}
        {weeks.map((week) => {
          const weekKey =
            week.find((day) => day !== undefined) ?? 'week-placeholder';
          return (
            <Box
              flexDirection="row"
              gap={0}
              key={String(weekKey)}
              margin={0}
              padding={0}
              width="100%"
            >
              {week.map((day, dayIndex) => (
                <DayCell
                  day={day}
                  dayData={dayData}
                  dayIndex={dayIndex}
                  isSelected={day === selectedDay}
                  key={String(day ?? `blank-${dayIndex}`)}
                  month={month}
                  year={year}
                />
              ))}
            </Box>
          );
        })}
      </Box>
    </Box>
  );
}

/** biome-ignore-all lint/style/noNestedTernary: The nested ternary structure is much cleaner in this specific case */
import { Box, Text, useStdout } from 'ink';
import { useState } from 'react';
import Calendar from './calendar.js';
import EditEntryModal from './components/edit-entry-modal.js';
import type { TimeEntry } from './day-pane.js';
import DayPane from './day-pane.js';
import { useTempoState } from './hooks/use-tempo-state.js';
import TimeEditModal from './time-edit-modal.js';

export default function App() {
  const { stdout } = useStdout();

  const {
    month,
    year,
    detail,
    dayData,
    actions: { handleMonthChange, handleOpenDay, handleSelectedDayChange },
  } = useTempoState();

  // Boolean-based modal states instead of string-based state machine
  const [showTimeEditModal, setShowTimeEditModal] = useState(false);
  const [showEditEntryModal, setShowEditEntryModal] = useState(false);
  const [editingEntry, setEditingEntry] = useState<TimeEntry | null>(null);

  const handleOpenTimeEditModal = () => {
    setShowTimeEditModal(true);
    // Close other modals
    setShowEditEntryModal(false);
    setEditingEntry(null);
  };

  const handleCloseTimeEditModal = () => {
    setShowTimeEditModal(false);
  };

  const handleOpenEditEntryModal = (entry: TimeEntry) => {
    setEditingEntry(entry);
    setShowEditEntryModal(true);
    // Close time edit modal to avoid conflicts
    setShowTimeEditModal(false);
  };

  const handleCloseEditEntryModal = () => {
    setShowEditEntryModal(false);
    setEditingEntry(null);
    // Return to time edit modal
    setShowTimeEditModal(true);
  };

  const handleSaveEntry = (_updatedEntry: TimeEntry) => {
    // TODO: Implement actual save logic
    handleCloseEditEntryModal();
  };

  return (
    <Box flexDirection="column" width="100%">
      <Text>
        {stdout.columns}x{stdout.rows}
      </Text>
      {/* Always render modals - they control their own visibility */}
      <TimeEditModal
        day={detail.day}
        entries={detail.entries}
        isOpen={showTimeEditModal}
        month={detail.month}
        onClose={handleCloseTimeEditModal}
        onEditEntry={handleOpenEditEntryModal}
        year={detail.year}
      />

      <EditEntryModal
        entry={editingEntry}
        isOpen={showEditEntryModal && !!editingEntry}
        onClose={handleCloseEditEntryModal}
        onSave={handleSaveEntry}
      />

      {/* Main content - show when no modals open */}
      {!(showTimeEditModal || showEditEntryModal) && (
        <Box flexDirection="row" width="100%">
          <Box width="75%">
            <Calendar
              dayData={dayData}
              month={month}
              {...(detail.month === month && detail.year === year
                ? { selectedDay: detail.day }
                : {})}
              onEnterDay={handleOpenDay}
              onMonthChange={handleMonthChange}
              onOpenTimeEdit={handleOpenTimeEditModal}
              onSelectedDayChange={handleSelectedDayChange}
              year={year}
            />
          </Box>
          <Box width="25%">
            <DayPane
              day={detail.day}
              entries={detail.entries}
              expected={detail.expected}
              month={detail.month}
              year={detail.year}
            />
          </Box>
        </Box>
      )}
    </Box>
  );
}

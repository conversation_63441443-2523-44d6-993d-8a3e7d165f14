import { Box, Text } from 'ink';

export type TimeEntry = {
  readonly ticket: string;
  readonly title: string;
  readonly description: string;
  readonly hours: number;
};

export type DayPaneProps = {
  readonly day: number;
  readonly month: number; // 1-12
  readonly year: number;
  readonly expected: number;
  readonly entries: TimeEntry[];
};

const monthNames = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

export default function DayPane({
  day,
  month,
  year,
  expected,
  entries,
}: DayPaneProps) {
  const total = entries.reduce((sum, e) => sum + e.hours, 0);
  const diff = total - expected;
  let diffColor: 'green' | 'red' | 'white' = 'white';
  if (diff > 0) {
    diffColor = 'green';
  } else if (diff < 0) {
    diffColor = 'red';
  }

  return (
    <Box
      borderStyle="single"
      flexDirection="column"
      height="100%"
      padding={1}
      width="100%"
    >
      <Text bold color="cyan">
        {monthNames[month - 1]} {day}, {year}
      </Text>
      <Box flexDirection="column" flexGrow={1}>
        {entries.length === 0 ? (
          <Box marginTop={1}>
            <Text>No entries</Text>
          </Box>
        ) : (
          entries.map((entry) => (
            <Box flexDirection="column" key={entry.ticket} marginTop={1}>
              <Box justifyContent="space-between" width="100%">
                <Box flexGrow={1} minWidth={0}>
                  <Text wrap="truncate">
                    <Text bold color="yellow">
                      {entry.ticket}
                    </Text>
                    {entry.title ? ` - ${entry.title}` : ''}
                  </Text>
                </Box>
                <Box flexShrink={0} marginLeft={1}>
                  <Text wrap="truncate">{entry.hours}h</Text>
                </Box>
              </Box>
              {entry.description ? (
                <Text color="gray">{entry.description}</Text>
              ) : null}
            </Box>
          ))
        )}
      </Box>
      <Box
        alignItems="flex-end"
        borderBottom={false}
        borderColor="gray"
        borderLeft={false}
        borderRight={false}
        borderStyle="single"
        borderTop
        flexDirection="column"
        flexShrink={0}
        marginTop={1}
        width="100%"
      >
        <Text bold color="cyan">
          Total: <Text color="white">{total}h</Text>
        </Text>
        <Text color="yellow">
          Expected: <Text color="white">{expected}h</Text>
        </Text>
        <Text color={diffColor}>
          ±:{' '}
          <Text color="white">
            {diff >= 0 ? '+' : ''}
            {diff}h
          </Text>
        </Text>
      </Box>
    </Box>
  );
}

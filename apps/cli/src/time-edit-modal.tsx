import { Box, Text, useInput } from 'ink';
import { useState } from 'react';
import type { TimeEntry } from './day-pane.js';

export type TimeEditModalProps = {
  readonly isOpen: boolean;
  readonly day: number;
  readonly month: number;
  readonly year: number;
  readonly entries: TimeEntry[];
  readonly onClose: () => void;
  readonly onEditEntry: (entry: TimeEntry) => void;
};

type TimeEntryItemProps = {
  readonly entry: TimeEntry;
  readonly isSelected: boolean;
};

function TimeEntryItem({ entry, isSelected }: TimeEntryItemProps) {
  return (
    <Box
      flexDirection="column"
      marginTop={1}
      paddingLeft={isSelected ? 1 : 0}
      paddingRight={isSelected ? 1 : 0}
      {...(isSelected && { backgroundColor: 'blue' })}
    >
      <Box justifyContent="space-between" width="100%">
        <Box flexGrow={1} minWidth={0}>
          <Text bold={isSelected} color="white" wrap="truncate">
            <Text bold color="yellow">
              {entry.ticket}
            </Text>
            {entry.title ? ` - ${entry.title}` : ''}
          </Text>
        </Box>
        <Box flexShrink={0} marginLeft={1}>
          <Text bold={isSelected} color="white" wrap="truncate">
            {entry.hours}h
          </Text>
        </Box>
      </Box>
      {entry.description && (
        <Box marginTop={1}>
          <Text color="gray">{entry.description}</Text>
        </Box>
      )}
    </Box>
  );
}

export default function TimeEditModal({
  isOpen,
  day,
  month,
  year,
  entries,
  onClose,
  onEditEntry,
}: TimeEditModalProps) {
  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const [selectedIndex, setSelectedIndex] = useState(0);

  // Handle keyboard input
  useInput((_input, key) => {
    if (!isOpen) {
      return; // Only handle input when open
    }

    if (key.escape) {
      onClose();
      return;
    }

    if (key.upArrow && entries.length > 0) {
      setSelectedIndex((prev: number) => Math.max(0, prev - 1));
      return;
    }

    if (key.downArrow && entries.length > 0) {
      setSelectedIndex((prev: number) =>
        Math.min(entries.length - 1, prev + 1)
      );
      return;
    }

    if (key.return && entries.length > 0 && entries[selectedIndex]) {
      onEditEntry(entries[selectedIndex]);
    }
  });

  if (!isOpen) {
    return null;
  }

  return (
    <Box
      alignItems="center"
      backgroundColor="black"
      height="100%"
      justifyContent="center"
      width="100%"
    >
      <Box
        alignItems="flex-start"
        borderColor="cyan"
        borderStyle="double"
        flexDirection="column"
        height="80%"
        padding={2}
        width="80%"
      >
        <Text bold color="cyan">
          Edit Time Entries - {monthNames[month - 1]} {day}, {year}
        </Text>

        <Box flexDirection="column" flexGrow={1} marginTop={1} width="100%">
          {entries.length === 0 ? (
            <Box marginTop={1}>
              <Text>No entries for this day</Text>
            </Box>
          ) : (
            entries.map((entry: TimeEntry, index: number) => (
              <TimeEntryItem
                entry={entry}
                isSelected={index === selectedIndex}
                key={`${entry.ticket}-${index}`}
              />
            ))
          )}
        </Box>

        <Box marginTop={1}>
          <Text color="gray">
            {entries.length > 0
              ? 'Use ↑↓ to navigate • ENTER to edit • ESC to close'
              : 'ESC to close'}
          </Text>
        </Box>
      </Box>
    </Box>
  );
}

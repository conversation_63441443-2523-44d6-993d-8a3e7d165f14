import { Box, Text, useInput } from 'ink';
import { useEffect, useState } from 'react';
import type { TimeEntry } from '../day-pane.js';

export type EditEntryModalProps = {
  readonly isOpen: boolean;
  readonly entry: TimeEntry | null;
  readonly onSave: (updatedEntry: TimeEntry) => void;
  readonly onClose: () => void;
};

// biome-ignore lint: This modal component needs all the logic for form handling
export default function EditEntryModal({
  isOpen,
  entry,
  onSave,
  onClose,
}: EditEntryModalProps) {
  const [ticket, setTicket] = useState('');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [hours, setHours] = useState('0');
  const [focusIndex, setFocusIndex] = useState(0);

  // Update form fields when entry prop changes
  useEffect(() => {
    if (entry) {
      setTicket(entry.ticket);
      setTitle(entry.title);
      setDescription(entry.description);
      setHours(entry.hours.toString());
      setFocusIndex(0); // Reset focus to first field
    }
  }, [entry]);

  const fields = ['ticket', 'title', 'description', 'hours', 'save'] as const;

  const handleSave = () => {
    const updatedEntry: TimeEntry = {
      ticket,
      title,
      description,
      hours: Number.parseFloat(hours) || 0,
    };
    onSave(updatedEntry);
    onClose();
  };

  const handleTextInput = (input: string, key: { backspace?: boolean }) => {
    if (focusIndex >= fields.length - 1) {
      return;
    }
    
    const setters = [setTicket, setTitle, setDescription, setHours];
    const setter = setters[focusIndex];
    
    if (setter) {
      if (key.backspace) {
        setter(prev => prev.slice(0, -1));
      } else if (input) {
        setter(prev => prev + input);
      }
    }
  };

  useInput((input, key) => {
    if (!isOpen) {
      return;
    }

    if (key.escape) {
      onClose();
      return;
    }

    if (key.tab || key.downArrow) {
      setFocusIndex((prev) => Math.min(prev + 1, fields.length - 1));
      return;
    }

    if (key.upArrow) {
      setFocusIndex((prev) => Math.max(prev - 1, 0));
      return;
    }

    if (key.return && focusIndex === fields.length - 1) {
      handleSave();
      return;
    }

    if (key.return && focusIndex < fields.length - 1) {
      setFocusIndex((prev) => Math.min(prev + 1, fields.length - 1));
      return;
    }

    handleTextInput(input, key);
  });

  if (!isOpen) {
    return null;
  }

  if (!entry) {
    return null;
  }

  return (
    <Box
      alignItems="center"
      backgroundColor="black"
      height="100%"
      justifyContent="center"
      width="100%"
    >
      <Box
        borderColor="cyan"
        borderStyle="double"
        flexDirection="column"
        height="80%"
        padding={2}
        width="70%"
      >
        <Text bold color="cyan">
          Edit Time Entry
        </Text>

        <Box flexDirection="column" marginTop={2} gap={1}>
          {/* Ticket Field */}
          <Box flexDirection="column">
            <Box marginBottom={1}>
              <Text color="gray" bold>
                Ticket:
              </Text>
            </Box>
            <Box
              backgroundColor={focusIndex === 0 ? 'blue' : 'blackBright'}
              paddingLeft={1}
              paddingRight={1}
              width="100%"
            >
              <Text color="white">
                {ticket || 'Enter ticket number'}{focusIndex === 0 ? '│' : ''}
              </Text>
            </Box>
          </Box>

          {/* Title Field */}
          <Box flexDirection="column">
            <Box marginBottom={1}>
              <Text color="gray" bold>
                Title:
              </Text>
            </Box>
            <Box
              backgroundColor={focusIndex === 1 ? 'blue' : 'blackBright'}
              paddingLeft={1}
              paddingRight={1}
              width="100%"
            >
              <Text color="white">
                {title || 'Enter title'}{focusIndex === 1 ? '│' : ''}
              </Text>
            </Box>
          </Box>

          {/* Description Field */}
          <Box flexDirection="column">
            <Box marginBottom={1}>
              <Text color="gray" bold>
                Description:
              </Text>
            </Box>
            <Box
              backgroundColor={focusIndex === 2 ? 'blue' : 'blackBright'}
              paddingLeft={1}
              paddingRight={1}
              width="100%"
            >
              <Text color="white">
                {description || 'Enter description'}{focusIndex === 2 ? '│' : ''}
              </Text>
            </Box>
          </Box>

          {/* Hours Field */}
          <Box flexDirection="column">
            <Box marginBottom={1}>
              <Text color="gray" bold>
                Hours:
              </Text>
            </Box>
            <Box
              backgroundColor={focusIndex === 3 ? 'blue' : 'blackBright'}
              paddingLeft={1}
              paddingRight={1}
              width="100%"
            >
              <Text color="white">
                {hours}{focusIndex === 3 ? '│' : ''}
              </Text>
            </Box>
          </Box>
        </Box>

        {/* Save Button */}
        <Box justifyContent="center" marginTop={2}>
          <Text
            bold
            color={focusIndex === 4 ? 'black' : 'white'}
            {...(focusIndex === 4 && { backgroundColor: 'cyan' })}
          >
            {' Save '}
          </Text>
        </Box>

        {/* Help Text */}
        <Box marginTop={2}>
          <Text color="gray">
            ESC to cancel • TAB/↑↓ to navigate • ENTER to save/next
          </Text>
        </Box>
      </Box>
    </Box>
  );
}
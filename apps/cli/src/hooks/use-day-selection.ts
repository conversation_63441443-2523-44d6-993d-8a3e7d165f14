import { useCallback, useState } from 'react';
import type { TimeEntry } from '../day-pane.js';
import { timeDataService } from '../services/time-data-service.js';

export type DetailData = {
  day: number;
  month: number;
  year: number;
  expected: number;
  entries: TimeEntry[];
};

export type DaySelectionState = {
  detail: DetailData;
  handleOpenDay: (day: number, m: number, y: number) => void;
  handleSelectedDayChange: (day: number) => void;
};

/**
 * Hook managing day selection and detail state.
 * Handles day-specific data and entry information.
 */
export function useDaySelection(
  month: number,
  year: number
): DaySelectionState {
  const currentDate = new Date();

  // Helper to get initial detail data
  const getInitialDetail = useCallback(() => {
    const today = currentDate.getDate();
    const daysInMonth = new Date(year, month, 0).getDate();
    let day = today;
    if (day < 1 || day > daysInMonth) {
      day = timeDataService.getFirstValidDay(year, month);
    }
    const dayData = timeDataService.generateDayData(year, month);
    const info = dayData[day] ?? { entered: 0, expected: 0 };
    return {
      day,
      month,
      year,
      expected: info.expected,
      entries: timeDataService.generateEntries(info.entered),
    };
  }, [currentDate, year, month]);

  const [detail, setDetail] = useState(getInitialDetail);

  const handleOpenDay = useCallback((day: number, m: number, y: number) => {
    const newDayData = timeDataService.generateDayData(y, m);
    const info = newDayData[day] ?? { entered: 0, expected: 0 };
    setDetail({
      day,
      month: m,
      year: y,
      expected: info.expected,
      entries: timeDataService.generateEntries(info.entered),
    });
  }, []);

  const handleSelectedDayChange = useCallback((day: number) => {
    setDetail((prev) => {
      const newDayData = timeDataService.generateDayData(prev.year, prev.month);
      const info = newDayData[day] ?? {
        entered: 0,
        expected: 0,
      };
      return {
        ...prev,
        day,
        expected: info.expected,
        entries: timeDataService.generateEntries(info.entered),
      };
    });
  }, []);

  return {
    detail,
    handleOpenDay,
    handleSelectedDayChange,
  };
}

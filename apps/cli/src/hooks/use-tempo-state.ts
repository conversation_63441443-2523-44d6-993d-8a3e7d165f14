import { useCallback } from 'react';
import {
  type DayData,
  timeDataService,
} from '../services/time-data-service.js';
import { useDateNavigation } from './use-date-navigation.js';
import { type DetailData, useDaySelection } from './use-day-selection.js';
import { useTimeData } from './use-time-data.js';

// DetailData moved to use-day-selection.ts

export type TempoState = {
  // Date state
  month: number;
  year: number;

  // UI state
  detail: DetailData;

  // Computed data
  dayData: Record<number, DayData>;

  // Actions
  actions: {
    handleMonthChange: (newMonth: number, newYear: number) => void;
    handleOpenDay: (day: number, m: number, y: number) => void;
    handleSelectedDayChange: (day: number) => void;
  };
};

/**
 * Custom hook orchestrating all tempo application state.
 * Composes focused hooks for better separation of concerns while maintaining same interface.
 */
export function useTempoState(): TempoState {
  const currentDate = new Date();

  // Use focused hooks
  const {
    month,
    year,
    handleMonthChange: baseHandleMonthChange,
  } = useDateNavigation();
  const { dayData } = useTimeData(month, year);
  const { detail, handleOpenDay, handleSelectedDayChange } = useDaySelection(
    month,
    year
  );

  // Enhanced month change handler that also updates day selection
  const handleMonthChange = useCallback(
    (newMonth: number, newYear: number) => {
      baseHandleMonthChange(newMonth, newYear);

      // After month/year update, show first valid day in new month
      const daysInMonth = new Date(newYear, newMonth, 0).getDate();
      let day = currentDate.getDate();
      if (
        newYear !== currentDate.getFullYear() ||
        newMonth !== currentDate.getMonth() + 1 ||
        day < 1 ||
        day > daysInMonth
      ) {
        // Not current month, pick first valid weekday
        day = timeDataService.getFirstValidDay(newYear, newMonth);
      }
      handleOpenDay(day, newMonth, newYear);
    },
    [baseHandleMonthChange, handleOpenDay, currentDate]
  );

  return {
    month,
    year,
    detail,
    dayData,
    actions: {
      handleMonthChange,
      handleOpenDay,
      handleSelectedDayChange,
    },
  };
}

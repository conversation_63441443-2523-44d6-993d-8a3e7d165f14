import { useCallback, useState } from 'react';

export type DateNavigationState = {
  month: number;
  year: number;
  handleMonthChange: (newMonth: number, newYear: number) => void;
};

/**
 * Hook managing month/year navigation state.
 * Handles calendar navigation and date transitions.
 */
export function useDateNavigation(): DateNavigationState {
  const currentDate = new Date();
  const [month, setMonth] = useState(currentDate.getMonth() + 1); // GetMonth() returns 0-11
  const [year, setYear] = useState(currentDate.getFullYear());

  const handleMonthChange = useCallback((newMonth: number, newYear: number) => {
    setMonth(newMonth);
    setYear(newYear);
  }, []);

  return {
    month,
    year,
    handleMonthChange,
  };
}

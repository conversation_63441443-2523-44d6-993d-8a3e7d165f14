import type { Key } from 'ink';
import { useCallback } from 'react';

export type NavigationCommand = {
  readonly type:
    | 'pageUp'
    | 'pageDown'
    | 'home'
    | 'left'
    | 'right'
    | 'up'
    | 'down'
    | 'enter'
    | 'initialize';
  readonly payload?: {
    readonly weekIndex?: number;
    readonly dayIndex?: number;
    readonly targetDayOfWeek?: number;
  };
};

export type NavigationState = {
  readonly weeks: ReadonlyArray<ReadonlyArray<number | undefined>>;
  readonly selectedDay: number | null;
  readonly month: number;
  readonly year: number;
};

export type NavigationCallbacks = {
  readonly onMonthChange: ((month: number, year: number) => void) | undefined;
  readonly onSelectedDayChange: ((day: number) => void) | undefined;
  readonly onEnterDay:
    | ((day: number, month: number, year: number) => void)
    | undefined;
  readonly onOpenTimeEdit: (() => void) | undefined;
};

export type NavigationHelpers = {
  readonly getDaysInMonth: (year: number, month: number) => number;
  readonly getFirstValidDay: () => number;
  readonly updateSelectedDay: (day: number) => void;
  readonly findDayPosition: (
    targetDay: number
  ) => { weekIndex: number; dayIndex: number } | null;
  readonly findLastValidDayInWeek: (
    week: ReadonlyArray<number | undefined>
  ) => number | undefined;
  readonly findFirstValidDayInWeek: (
    week: ReadonlyArray<number | undefined>
  ) => number | undefined;
  readonly findLastDayOfWeekInMonth: (
    targetYear: number,
    targetMonth: number,
    dayOfWeek: number
  ) => number;
  readonly findFirstDayOfWeekInMonth: (
    targetYear: number,
    targetMonth: number,
    dayOfWeek: number
  ) => number;
};

const daysInWeek = 7;

function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month, 0).getDate();
}

export function useCalendarNavigation(
  state: NavigationState,
  callbacks: NavigationCallbacks,
  helpers: NavigationHelpers
) {
  const { weeks, selectedDay, month, year } = state;
  const { onMonthChange, onOpenTimeEdit } = callbacks;
  const {
    updateSelectedDay,
    findDayPosition,
    findLastValidDayInWeek,
    findFirstValidDayInWeek,
    findLastDayOfWeekInMonth,
    findFirstDayOfWeekInMonth,
    getFirstValidDay,
  } = helpers;

  // Helper function to get the day of week for the currently selected day
  const getCurrentDayOfWeek = useCallback((): number => {
    if (!selectedDay) {
      return 0; // Sunday as fallback
    }
    const date = new Date(year, month - 1, selectedDay);
    return date.getDay();
  }, [selectedDay, year, month]);

  // Month navigation helper functions
  const navigateToPreviousMonth = useCallback(
    (targetDayOfWeek?: number) => {
      if (!onMonthChange) {
        return;
      }

      let newMonth = month - 1;
      let newYear = year;

      if (newMonth < 1) {
        newMonth = 12;
        newYear = year - 1;
      }

      onMonthChange(newMonth, newYear);

      // If a target day of week is specified, select the last occurrence of that day in the previous month
      if (targetDayOfWeek !== undefined) {
        const targetDay = findLastDayOfWeekInMonth(
          newYear,
          newMonth,
          targetDayOfWeek
        );
        updateSelectedDay(targetDay);
      }
    },
    [month, year, onMonthChange, findLastDayOfWeekInMonth, updateSelectedDay]
  );

  const navigateToNextMonth = useCallback(
    (targetDayOfWeek?: number) => {
      if (!onMonthChange) {
        return;
      }

      let newMonth = month + 1;
      let newYear = year;

      if (newMonth > 12) {
        newMonth = 1;
        newYear = year + 1;
      }

      onMonthChange(newMonth, newYear);

      // If a target day of week is specified, select the first occurrence of that day in the next month
      if (targetDayOfWeek !== undefined) {
        const targetDay = findFirstDayOfWeekInMonth(
          newYear,
          newMonth,
          targetDayOfWeek
        );
        updateSelectedDay(targetDay);
      }
    },
    [month, year, onMonthChange, findFirstDayOfWeekInMonth, updateSelectedDay]
  );

  // Helper function to navigate to previous month from first day
  const navigateToPreviousMonthFromFirstDay = useCallback(() => {
    let newMonth = month - 1;
    let newYear = year;

    if (newMonth < 1) {
      newMonth = 12;
      newYear = year - 1;
    }

    const lastDayOfPreviousMonth = getDaysInMonth(newYear, newMonth);
    onMonthChange?.(newMonth, newYear);
    updateSelectedDay(lastDayOfPreviousMonth);
  }, [month, year, onMonthChange, updateSelectedDay]);

  // Helper function to navigate to next month from last day
  const navigateToNextMonthFromLastDay = useCallback(() => {
    let newMonth = month + 1;
    let newYear = year;

    if (newMonth > 12) {
      newMonth = 1;
      newYear = year + 1;
    }

    onMonthChange?.(newMonth, newYear);
    updateSelectedDay(1);
  }, [month, year, onMonthChange, updateSelectedDay]);

  // Helper function to navigate to same day in previous month
  const navigateToPreviousMonthSameDay = useCallback(() => {
    if (!onMonthChange) {
      return;
    }

    let newMonth = month - 1;
    let newYear = year;

    if (newMonth < 1) {
      newMonth = 12;
      newYear = year - 1;
    }

    onMonthChange(newMonth, newYear);

    // If no day is selected, use the first valid day of the new month
    if (!selectedDay) {
      const firstValidDay = getFirstValidDay();
      updateSelectedDay(firstValidDay);
      return;
    }

    // Try to maintain the same day number, but adjust if the day doesn't exist in the new month
    const daysInNewMonth = getDaysInMonth(newYear, newMonth);
    const targetDay = Math.min(selectedDay, daysInNewMonth);
    updateSelectedDay(targetDay);
  }, [
    selectedDay,
    month,
    year,
    onMonthChange,
    updateSelectedDay,
    getFirstValidDay,
  ]);

  // Helper function to navigate to same day in next month
  const navigateToNextMonthSameDay = useCallback(() => {
    if (!onMonthChange) {
      return;
    }

    let newMonth = month + 1;
    let newYear = year;

    if (newMonth > 12) {
      newMonth = 1;
      newYear = year + 1;
    }

    onMonthChange(newMonth, newYear);

    // If no day is selected, use the first valid day of the new month
    if (!selectedDay) {
      const firstValidDay = getFirstValidDay();
      updateSelectedDay(firstValidDay);
      return;
    }

    // Try to maintain the same day number, but adjust if the day doesn't exist in the new month
    const daysInNewMonth = getDaysInMonth(newYear, newMonth);
    const targetDay = Math.min(selectedDay, daysInNewMonth);
    updateSelectedDay(targetDay);
  }, [
    selectedDay,
    month,
    year,
    onMonthChange,
    updateSelectedDay,
    getFirstValidDay,
  ]);

  // Helper function to navigate to today's date
  const navigateToToday = useCallback(() => {
    if (!onMonthChange) {
      return;
    }

    const today = new Date();
    const currentMonth = today.getMonth() + 1; // getMonth() returns 0-11
    const currentYear = today.getFullYear();
    const currentDay = today.getDate();

    // Only change month/year if different from current
    if (month !== currentMonth || year !== currentYear) {
      onMonthChange(currentMonth, currentYear);
    }

    // Always update to today's date
    updateSelectedDay(currentDay);
  }, [month, year, onMonthChange, updateSelectedDay]);

  // Helper function for within-week left navigation
  const navigateLeftWithinWeek = (
    weekIndex: number,
    dayIndex: number
  ): boolean => {
    if (dayIndex > 0) {
      const currentWeek = weeks[weekIndex];
      const prevDay = currentWeek?.[dayIndex - 1];
      if (prevDay !== undefined) {
        updateSelectedDay(prevDay);
        return true;
      }
    }
    return false;
  };

  // Helper function for between-week left navigation
  const navigateLeftBetweenWeeks = (weekIndex: number): boolean => {
    if (weekIndex > 0) {
      const prevWeek = weeks[weekIndex - 1];
      if (prevWeek) {
        const lastValidDay = findLastValidDayInWeek(prevWeek);
        if (lastValidDay !== undefined) {
          updateSelectedDay(lastValidDay);
          return true;
        }
      }
    }
    return false;
  };

  // Navigation helper functions
  const navigateLeft = (weekIndex: number, dayIndex: number) => {
    // Check if we're on the first day of the month
    if (selectedDay === 1) {
      navigateToPreviousMonthFromFirstDay();
      return;
    }

    // Try within-week navigation first
    if (navigateLeftWithinWeek(weekIndex, dayIndex)) {
      return;
    }

    // Try between-week navigation
    if (navigateLeftBetweenWeeks(weekIndex)) {
      return;
    }

    // At the beginning of the month grid, navigate to previous month
    const currentDayOfWeek = getCurrentDayOfWeek();
    navigateToPreviousMonth(currentDayOfWeek);
  };

  // Helper function for within-week right navigation
  const navigateRightWithinWeek = (
    weekIndex: number,
    dayIndex: number
  ): boolean => {
    if (dayIndex < daysInWeek - 1) {
      const currentWeek = weeks[weekIndex];
      const nextDay = currentWeek?.[dayIndex + 1];
      if (nextDay !== undefined) {
        updateSelectedDay(nextDay);
        return true;
      }
    }
    return false;
  };

  // Helper function for between-week right navigation
  const navigateRightBetweenWeeks = (weekIndex: number): boolean => {
    if (weekIndex < weeks.length - 1) {
      const nextWeek = weeks[weekIndex + 1];
      if (nextWeek) {
        const firstValidDay = findFirstValidDayInWeek(nextWeek);
        if (firstValidDay !== undefined) {
          updateSelectedDay(firstValidDay);
          return true;
        }
      }
    }
    return false;
  };

  const navigateRight = (weekIndex: number, dayIndex: number) => {
    // Check if we're on the last day of the month
    const daysInCurrentMonth = getDaysInMonth(year, month);
    if (selectedDay === daysInCurrentMonth) {
      navigateToNextMonthFromLastDay();
      return;
    }

    // Try within-week navigation first
    if (navigateRightWithinWeek(weekIndex, dayIndex)) {
      return;
    }

    // Try between-week navigation
    if (navigateRightBetweenWeeks(weekIndex)) {
      return;
    }

    // At the end of the month grid, navigate to next month
    const currentDayOfWeek = getCurrentDayOfWeek();
    navigateToNextMonth(currentDayOfWeek);
  };

  const navigateUp = (weekIndex: number, dayIndex: number) => {
    if (weekIndex <= 0) {
      // At the top of the month, navigate to previous month
      const currentDayOfWeek = getCurrentDayOfWeek();
      navigateToPreviousMonth(currentDayOfWeek);
      return;
    }

    const prevWeek = weeks[weekIndex - 1];
    if (!prevWeek) {
      return;
    }

    const upDay = prevWeek[dayIndex];
    if (upDay !== undefined) {
      updateSelectedDay(upDay);
      return;
    }

    // If the cell directly above is empty, navigate to previous month
    const currentDayOfWeek = getCurrentDayOfWeek();
    navigateToPreviousMonth(currentDayOfWeek);
  };

  const navigateDown = (weekIndex: number, dayIndex: number) => {
    if (weekIndex >= weeks.length - 1) {
      // At the bottom of the month, navigate to next month
      const currentDayOfWeek = getCurrentDayOfWeek();
      navigateToNextMonth(currentDayOfWeek);
      return;
    }

    const nextWeek = weeks[weekIndex + 1];
    if (!nextWeek) {
      return;
    }

    const downDay = nextWeek[dayIndex];
    if (downDay !== undefined) {
      updateSelectedDay(downDay);
      return;
    }

    // If the cell directly below is empty, navigate to next month
    const currentDayOfWeek = getCurrentDayOfWeek();
    navigateToNextMonth(currentDayOfWeek);
  };

  // Command handlers
  const navigationCommands = {
    pageUp: () => navigateToPreviousMonthSameDay(),
    pageDown: () => navigateToNextMonthSameDay(),
    home: () => navigateToToday(),
    left: (weekIndex: number, dayIndex: number) =>
      navigateLeft(weekIndex, dayIndex),
    right: (weekIndex: number, dayIndex: number) =>
      navigateRight(weekIndex, dayIndex),
    up: (weekIndex: number, dayIndex: number) =>
      navigateUp(weekIndex, dayIndex),
    down: (weekIndex: number, dayIndex: number) =>
      navigateDown(weekIndex, dayIndex),
    enter: () => {
      if (onOpenTimeEdit && selectedDay !== null) {
        onOpenTimeEdit();
      }
    },
    initialize: () => {
      const today = new Date();
      let initialDay: number;
      if (today.getMonth() + 1 === month && today.getFullYear() === year) {
        initialDay = today.getDate();
      } else {
        initialDay = getFirstValidDay();
      }
      updateSelectedDay(initialDay);
    },
  };

  // Handler for special keys (Page Up/Down, Home, Enter)
  const handleSpecialKeys = useCallback(
    (_input: string, key: Key): boolean => {
      if (key.pageUp) {
        navigationCommands.pageUp();
        return true;
      }
      if (key.pageDown) {
        navigationCommands.pageDown();
        return true;
      }
      if (_input === 'h') {
        navigationCommands.home();
        return true;
      }
      if (key.return && onOpenTimeEdit && selectedDay !== null) {
        navigationCommands.enter();
        return true;
      }
      return false;
    },
    [
      navigationCommands.pageUp,
      navigationCommands.pageDown,
      navigationCommands.home,
      navigationCommands.enter,
      onOpenTimeEdit,
      selectedDay,
    ]
  );

  // Handler for arrow key navigation
  const handleArrowKeys = useCallback(
    (key: Key): boolean => {
      if (!selectedDay) {
        navigationCommands.initialize();
        return true;
      }

      const currentPosition = findDayPosition(selectedDay);
      if (!currentPosition) {
        return true;
      }

      const { weekIndex, dayIndex } = currentPosition;

      if (key.leftArrow) {
        navigationCommands.left(weekIndex, dayIndex);
        return true;
      }
      if (key.rightArrow) {
        navigationCommands.right(weekIndex, dayIndex);
        return true;
      }
      if (key.upArrow) {
        navigationCommands.up(weekIndex, dayIndex);
        return true;
      }
      if (key.downArrow) {
        navigationCommands.down(weekIndex, dayIndex);
        return true;
      }
      return false;
    },
    [
      selectedDay,
      findDayPosition,
      navigationCommands.initialize,
      navigationCommands.left,
      navigationCommands.right,
      navigationCommands.up,
      navigationCommands.down,
    ]
  );

  // Main keyboard handler
  const handleKeyboardInput = useCallback(
    (_input: string, key: Key) => {
      // Try special keys first
      if (handleSpecialKeys(_input, key)) {
        return;
      }

      // Then try arrow keys
      handleArrowKeys(key);
    },
    [handleSpecialKeys, handleArrowKeys]
  );

  return {
    handleKeyboardInput,
    navigationCommands,
  };
}

import { useMemo } from 'react';
import {
  type DayData,
  timeDataService,
} from '../services/time-data-service.js';

export type TimeDataState = {
  dayData: Record<number, DayData>;
};

/**
 * Hook managing time data computation.
 * Handles data generation and service integration.
 */
export function useTimeData(month: number, year: number): TimeDataState {
  const dayData = useMemo(() => {
    return timeDataService.generateDayData(year, month);
  }, [year, month]);

  return {
    dayData,
  };
}

import type { TimeEntry } from '../day-pane.js';

export type DayData = {
  entered: number;
  expected: number;
};

/**
 * Service responsible for generating mock time tracking data.
 * Maintains exact same random data patterns as original implementation.
 */
export class TimeDataService {
  /**
   * Generates a random ticket ID in format ABC-123
   */
  private randomTicket(): string {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const part = () =>
      letters.charAt(Math.floor(Math.random() * letters.length)) +
      letters.charAt(Math.floor(Math.random() * letters.length)) +
      letters.charAt(Math.floor(Math.random() * letters.length));
    const num = Math.floor(Math.random() * 900) + 100;
    return `${part()}-${num}`;
  }

  /**
   * Generates a random title from predefined options
   */
  private randomTitle(): string {
    const titles = [
      'Fix bug',
      'Implement feature',
      'Code review',
      'Write tests',
      'Refactor modules',
    ];
    const index = Math.floor(Math.random() * titles.length);
    return titles[index] ?? '';
  }

  /**
   * Generates a random description from predefined options
   */
  private randomDescription(): string {
    const descriptions = [
      'Worked on issue',
      'Completed tasks',
      'Updated documentation',
      'Investigated problem',
    ];
    const index = Math.floor(Math.random() * descriptions.length);
    return descriptions[index] ?? '';
  }

  /**
   * Generates time entries for a given total hours
   * @param total Total hours to distribute across entries
   * @returns Array of TimeEntry objects
   */
  generateEntries(total: number): TimeEntry[] {
    if (total <= 0) {
      return [];
    }
    const count = Math.min(total, Math.floor(Math.random() * 3) + 1);
    let remaining = total;
    const entries: TimeEntry[] = [];
    for (let i = 0; i < count; i++) {
      const max = remaining - (count - i - 1);
      const hours =
        i === count - 1 ? remaining : Math.floor(Math.random() * max) + 1;
      remaining -= hours;
      entries.push({
        ticket: this.randomTicket(),
        title: this.randomTitle(),
        description: this.randomDescription(),
        hours,
      });
    }
    return entries;
  }

  /**
   * Generates day data for all days in a given month/year
   * @param year Full year (e.g., 2024)
   * @param month Month (1-12)
   * @returns Record mapping day numbers to DayData
   */
  generateDayData(year: number, month: number): Record<number, DayData> {
    const daysInMonth = new Date(year, month, 0).getDate();
    const data: Record<number, DayData> = {};
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day);
      const weekday = date.getDay();
      const expected = weekday === 0 || weekday === 6 ? 0 : 8;
      const entered =
        weekday === 0 || weekday === 6 ? 0 : Math.floor(Math.random() * 9);
      data[day] = { entered, expected };
    }

    return data;
  }

  /**
   * Gets the first valid weekday in a given month/year
   * @param year Full year
   * @param month Month (1-12)
   * @returns First weekday number (1-31)
   */
  getFirstValidDay(year: number, month: number): number {
    const daysInMonth = new Date(year, month, 0).getDate();
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day);
      const weekday = date.getDay();
      if (weekday !== 0 && weekday !== 6) {
        return day;
      }
    }
    return 1; // fallback
  }
}

// Export singleton instance
export const timeDataService = new TimeDataService();

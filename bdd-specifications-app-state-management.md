# BDD Test Specifications: App.tsx State Management (Risk Score 8.0)

## Overview
App.tsx contains high-complexity state management with mixed concerns that must be preserved during extraction. These specifications cover the critical behaviors for safe refactoring.

---

## Feature 1: Data Generation Service

### Background: Mock Data Generation
The system generates consistent random data for time tracking entries and daily summaries.

### Scenario 1.1: Random Ticket Generation
```gherkin
Given the data generation service
When I request a random ticket identifier
Then it should return a ticket in format "XXX-###"
And the format should be three uppercase letters, dash, three digits
And the digits should be between 100-999
And each call should produce different results
```

### Scenario 1.2: Random Title Selection
```gherkin
Given the predefined title options: ["Fix bug", "Implement feature", "Code review", "Write tests", "Refactor modules"]
When I request a random title
Then it should return one of the predefined options
And the distribution should be reasonably random across calls
```

### Scenario 1.3: Time Entry Generation
```gherkin
Given a total hours value of <totalHours>
When I generate time entries
Then the sum of all entry hours should equal <totalHours>
And there should be between 1 and 3 entries maximum
And each entry should have a valid ticket, title, description, and hours > 0
And the last entry should consume any remaining hours

Examples:
| totalHours | expectedBehavior |
| 0          | empty array      |
| 1          | single 1h entry  |
| 8          | 1-3 entries totaling 8h |
```

### Scenario 1.4: Daily Data Generation for Month
```gherkin
Given a year <year> and month <month>
When I generate daily data for the month
Then it should return data for every day in the month
And weekends (Saturday/Sunday) should have entered=0 and expected=0
And weekdays should have expected=8 and entered=0-8 (random)
And the number of days should match the calendar month

Examples:
| year | month | expectedDays |
| 2024 | 2     | 29          |
| 2024 | 4     | 30          |
| 2024 | 12    | 31          |
```

---

## Feature 2: Application State Synchronization

### Background: Month/Year Navigation State
The application maintains synchronized state across Calendar and DayPane components when navigating between months and years.

### Scenario 2.1: Month Change Propagation
```gherkin
Given the application is showing month 3 (March) year 2024
And day 15 is selected in the current month
When the user navigates to month 4 (April) year 2024
Then the Calendar should display April 2024
And the DayPane should show the first valid weekday of April 2024
And the day data should be regenerated for April
And the selected day should be updated to the first valid weekday
```

### Scenario 2.2: Year Change with Day Adjustment
```gherkin
Given the application is showing March 31, 2024
When the user navigates to February 2024 (28 days)
Then the selected day should be adjusted to the first valid weekday of February
And the DayPane should display the adjusted day
And no invalid date should be selected
```

### Scenario 2.3: Current Month Detection
```gherkin
Given today's date is March 15, 2024
And the application is showing March 2024
When the user navigates to a different month and back to March 2024
Then the selected day should return to day 15 (today)
And the DayPane should show March 15, 2024 data
```

---

## Feature 3: Day Selection and Detail Display

### Background: Day Detail Coordination
When a day is selected in the Calendar, the DayPane must display corresponding time entry data.

### Scenario 3.1: Day Selection Synchronization
```gherkin
Given the Calendar is displaying March 2024
And day 10 is currently selected
When the user selects day 15
Then the DayPane should immediately show "March 15, 2024"
And the time entries should be regenerated for day 15
And the expected hours should reflect day 15's weekday status
And the Calendar should highlight day 15 as selected
```

### Scenario 3.2: Weekend Day Selection
```gherkin
Given the Calendar is displaying March 2024
When the user selects a Saturday (day 2)
Then the DayPane should show "March 2, 2024"
And the expected hours should be 0
And the time entries should be empty
And the total should show 0h
And the difference should show ±: 0h
```

### Scenario 3.3: Weekday Entry Display
```gherkin
Given the user selects a weekday with 5 entered hours
When the DayPane displays the day details
Then it should show "Expected: 8h"
And it should show "Total: 5h" 
And it should show "±: -3h" in red color
And it should display 1-3 time entries totaling 5 hours
```

---

## Feature 4: Time Edit Pane Integration

### Background: Modal Time Edit Functionality
The TimeEditPane appears as a modal overlay for editing time entries.

### Scenario 4.1: Time Edit Pane Opening
```gherkin
Given the Calendar is focused and day 15 is selected
When the user presses Enter
Then the TimeEditPane should open as a modal overlay
And the Calendar and DayPane should be hidden
And the TimeEditPane should display day 15 details
And the modal should be centered on screen
```

### Scenario 4.2: Time Edit Pane Closing
```gherkin
Given the TimeEditPane is open for day 15
When the user closes the TimeEditPane
Then the modal should disappear
And the Calendar and DayPane should be visible again
And the Calendar should still show day 15 as selected
And the DayPane should still show day 15 data
```

### Scenario 4.3: State Persistence During Modal
```gherkin
Given day 20 is selected and TimeEditPane is open
When the user closes the TimeEditPane
Then the application should return to the same state
And day 20 should still be selected
And the month/year should be unchanged
And the DayPane should show the same day 20 data
```

---

## Feature 5: Component Layout Orchestration

### Background: Dynamic Layout Management
The application switches between two layout modes: Calendar+DayPane view and TimeEditPane modal.

### Scenario 5.1: Default Layout Structure
```gherkin
Given the application starts with no TimeEditPane open
Then the layout should show:
  - Calendar component at 75% width
  - DayPane component at 25% width
  - Both in a horizontal row
  - Terminal dimensions displayed at top
```

### Scenario 5.2: Modal Layout Switch
```gherkin
Given the default Calendar+DayPane layout is showing
When the TimeEditPane opens
Then the layout should switch to:
  - TimeEditPane centered as modal overlay
  - Calendar and DayPane completely hidden
  - Full-screen modal container
  - No horizontal row layout
```

### Scenario 5.3: Layout Restoration
```gherkin
Given the TimeEditPane modal is active
When the TimeEditPane closes
Then the layout should restore to:
  - Calendar at 75% width on left
  - DayPane at 25% width on right
  - Horizontal row layout
  - All previous state preserved
```

---

## Feature 6: Date Boundary Handling

### Background: Complex Date Calculations
The application handles month boundaries, leap years, and weekday calculations correctly.

### Scenario 6.1: Month Boundary Navigation
```gherkin
Given the user is viewing January 31, 2024
When navigating to February 2024
Then the selected day should adjust to a valid February date
And it should be the first valid weekday (not weekend)
And the day should not exceed February's day count (29 in 2024)
```

### Scenario 6.2: Leap Year Handling
```gherkin
Given the year is 2024 (leap year)
When generating data for February
Then February should have 29 days of data
And day 29 should be properly handled
And weekday calculations should be correct for day 29

Given the year is 2023 (non-leap year)  
When generating data for February
Then February should have 28 days of data
And day 29 should not exist in the data
```

### Scenario 6.3: First Valid Day Calculation
```gherkin
Given a month where day 1 falls on a weekend
When calculating the first valid day
Then it should find the first weekday (Monday-Friday)
And skip Saturday (day 0) and Sunday (day 6)
And return the correct day number
And handle edge cases where entire week starts on weekend
```

---

## Feature 7: State Update Cascades

### Background: State Change Propagation
When core state changes (month/year), multiple derived states must update consistently.

### Scenario 7.1: Month Change Cascade
```gherkin
Given current state: March 2024, day 15 selected
When the month changes to April 2024
Then the following should update in sequence:
  1. month state = 4
  2. year state = 2024  
  3. day data regenerated for April
  4. selected day recalculated for April
  5. detail state updated with new day/month/year
  6. DayPane entries regenerated
  7. Calendar highlights new selected day
```

### Scenario 7.2: Initial State Calculation
```gherkin
Given the application starts on March 15, 2024
Then the initial state should be:
  - month = 3
  - year = 2024
  - selectedDay = 15 (today)
  - dayData generated for March 2024
  - detail showing March 15 with appropriate entries
  - Calendar highlighting day 15
```

### Scenario 7.3: State Consistency Check
```gherkin
Given any valid application state
Then the following should always be consistent:
  - detail.month matches Calendar month
  - detail.year matches Calendar year  
  - detail.day matches Calendar selectedDay
  - DayPane data matches detail entries
  - Generated data matches current month/year
```

---

## Integration Test Scenarios

### Scenario I.1: Complete Navigation Flow
```gherkin
Given the application starts in current month
When the user:
  1. Navigates to previous month
  2. Selects a weekday
  3. Opens TimeEditPane
  4. Closes TimeEditPane
  5. Navigates to next month
Then each step should maintain state consistency
And the final state should show correct month/day data
```

### Scenario I.2: Cross-Component Data Flow
```gherkin
Given day 10 is selected with 6 entered hours
When the Calendar component triggers day selection
Then the state change should flow:
  1. Calendar calls onSelectedDayChange(10)
  2. App updates detail state
  3. DayPane receives new props
  4. DayPane displays updated entries and totals
  5. All components show consistent day 10 data
```

### Scenario I.3: Error Recovery
```gherkin
Given invalid date state (day 32 of March)
When the application recalculates state
Then it should recover to a valid date
And maintain component consistency
And not crash or show invalid data
```

---

## Performance and Consistency Scenarios

### Scenario P.1: Data Generation Consistency
```gherkin
Given the same year/month parameters
When generateDayData is called multiple times
Then it should return identical data structures
But time entries should have random variation
And the random seed should be implicit per day
```

### Scenario P.2: State Update Batching
```gherkin
Given rapid month navigation (multiple clicks)
When state updates are triggered
Then the final state should be consistent
And intermediate states should not cause display flicker
And all components should sync to final state
```

---

## Refactoring Safety Requirements

These scenarios ensure that when extracting concerns into separate services:

1. **DataGenerationService**: All random data generation behaviors preserved
2. **StateCoordinationService**: All month/year/day synchronization preserved  
3. **ComponentOrchestrationService**: All layout and modal behaviors preserved
4. **DateCalculationService**: All boundary and calendar logic preserved

Each service must pass all relevant scenarios before extraction is complete.
# Implementation Plan: New Modal Architecture

## Files to Modify

### 1. apps/cli/src/app.tsx
Replace string-based state machine with boolean-based modal states.

#### Current State Structure (Lines 26-28)
```typescript
type ViewState = 'main' | 'timeEdit' | 'editEntry';
const [currentView, setCurrentView] = useState<ViewState>('main');
const [editingEntry, setEditingEntry] = useState<TimeEntry | null>(null);
```

#### New State Structure
```typescript
const [showTimeEditModal, setShowTimeEditModal] = useState(false);
const [showEditEntryModal, setShowEditEntryModal] = useState(false);
const [editingEntry, setEditingEntry] = useState<TimeEntry | null>(null);
```

#### Current Handler Functions (Lines 30-39)
```typescript
const handleOpenTimeEditModal = () => setCurrentView('timeEdit');
const handleCloseTimeEditModal = () => setCurrentView('main');
const handleOpenEditEntryModal = (entry: TimeEntry) => {
  setEditingEntry(entry);
  setCurrentView('editEntry');
};
const handleCloseEditEntryModal = () => {
  setEditingEntry(null);
  setCurrentView('timeEdit');
};
```

#### New Handler Functions
```typescript
const handleOpenTimeEditModal = () => {
  setShowTimeEditModal(true);
  // Close other modals
  setShowEditEntryModal(false);
  setEditingEntry(null);
};

const handleCloseTimeEditModal = () => {
  setShowTimeEditModal(false);
};

const handleOpenEditEntryModal = (entry: TimeEntry) => {
  setEditingEntry(entry);
  setShowEditEntryModal(true);
  // Close time edit modal to avoid conflicts
  setShowTimeEditModal(false);
};

const handleCloseEditEntryModal = () => {
  setShowEditEntryModal(false);
  setEditingEntry(null);
  // Return to time edit modal
  setShowTimeEditModal(true);
};

const handleSaveEntry = (updatedEntry: TimeEntry) => {
  // TODO: Implement actual save logic
  console.log('Saving entry:', updatedEntry);
  handleCloseEditEntryModal();
};
```

#### Current Render Logic (Lines 46-107)
```typescript
{currentView === 'timeEdit' ? (
  // TimeEditModal overlay - render only the popup
  <Box alignItems="center" height="100%" justifyContent="center" width="100%">
    <TimeEditModal />
  </Box>
) : currentView === 'editEntry' && editingEntry ? (
  // EditEntryModal overlay - render only the popup
  <Box alignItems="center" height="100%" justifyContent="center" width="100%">
    <EditEntryModal />
  </Box>
) : (
  // Main content - render calendar and day pane
  <Box flexDirection="row" width="100%">
    <Calendar />
    <DayPane />
  </Box>
)}
```

#### New Render Logic
```typescript
{/* Always render modals - they control their own visibility */}
<TimeEditModal 
  isOpen={showTimeEditModal}
  day={detail.day}
  entries={detail.entries}
  month={detail.month}
  year={detail.year}
  onClose={handleCloseTimeEditModal}
  onEditEntry={handleOpenEditEntryModal}
/>

<EditEntryModal
  isOpen={showEditEntryModal && !!editingEntry}
  entry={editingEntry}
  onClose={handleCloseEditEntryModal}
  onSave={handleSaveEntry}
/>

{/* Main content - show when no modals open */}
{!showTimeEditModal && !showEditEntryModal && (
  <Box flexDirection="row" width="100%">
    <Box width="75%">
      <Calendar
        dayData={dayData}
        month={month}
        {...(detail.month === month && detail.year === year
          ? { selectedDay: detail.day }
          : {})}
        onEnterDay={handleOpenDay}
        onMonthChange={handleMonthChange}
        onOpenTimeEdit={handleOpenTimeEditModal}
        onSelectedDayChange={handleSelectedDayChange}
        year={year}
      />
    </Box>
    <Box width="25%">
      <DayPane
        day={detail.day}
        entries={detail.entries}
        expected={detail.expected}
        month={detail.month}
        year={detail.year}
      />
    </Box>
  </Box>
)}
```

### 2. apps/cli/src/time-edit-modal.tsx

#### Add isOpen Prop to TimeEditModalProps (Line 5)
```typescript
export type TimeEditModalProps = {
  readonly isOpen: boolean;  // ADD THIS LINE
  readonly day: number;
  readonly month: number;
  readonly year: number;
  readonly entries: TimeEntry[];
  readonly onClose: () => void;
  readonly onEditEntry: (entry: TimeEntry) => void;
};
```

#### Update Component Function (Line 52)
```typescript
export default function TimeEditModal({
  isOpen,  // ADD THIS PARAMETER
  day,
  month,
  year,
  entries,
  onClose,
  onEditEntry,
}: TimeEditModalProps) {
```

#### Add Conditional Input Handling (Line 78)
```typescript
useInput((_input, key) => {
  if (!isOpen) return;  // ADD THIS LINE - Only handle input when open
  
  if (key.escape) {
    onClose();
    return;
  }
  // ... rest of input handling unchanged
});
```

#### Add Conditional Rendering (Line 101)
```typescript
if (!isOpen) return null;  // ADD THIS LINE

return (
  <Box
    alignItems="center"        // ADD THESE LINES
    backgroundColor="black"    // for overlay styling
    height="100%"              // to match EditEntryModal
    justifyContent="center"    // centering behavior
    position="absolute"        // overlay positioning
    width="100%"
  >
    <Box
      alignItems="flex-start"  // MOVE EXISTING STYLING HERE
      borderColor="cyan"
      borderStyle="double"
      flexDirection="column"
      height="80%"
      padding={2}
      width="80%"
    >
      {/* existing modal content unchanged */}
    </Box>
  </Box>
);
```

### 3. apps/cli/src/components/edit-entry-modal.tsx

#### Add isOpen Prop to EditEntryModalProps (Line 5)
```typescript
export type EditEntryModalProps = {
  readonly isOpen: boolean;  // ADD THIS LINE
  readonly entry: TimeEntry;
  readonly onSave: (updatedEntry: TimeEntry) => void;
  readonly onClose: () => void;
};
```

#### Update Component Function (Line 11)
```typescript
export default function EditEntryModal({
  isOpen,  // ADD THIS PARAMETER
  entry,
  onSave,
  onClose,
}: EditEntryModalProps) {
```

#### Add Conditional Input Handling (Line 80)
```typescript
useInput((input, key) => {
  if (!isOpen) return;  // ADD THIS LINE - Only handle input when open
  
  if (key.escape) {
    onClose();
    return;
  }
  // ... rest of input handling unchanged
});
```

#### Add Conditional Rendering (Line 98)
```typescript
if (!isOpen) return null;  // ADD THIS LINE

// Rest of component unchanged - it already has proper overlay styling
return (
  <Box
    alignItems="center"
    backgroundColor="black" 
    height="100%"
    justifyContent="center"
    position="absolute"
    width="100%"
  >
    {/* existing modal content unchanged */}
  </Box>
);
```

## Key Benefits After Implementation

1. **Fixed EditEntryModal**: Removes double overlay styling conflict
2. **Self-Contained Modals**: Each modal controls its own visibility logic
3. **Clear State Management**: Boolean states are more readable than string constants
4. **Easy Modal Coordination**: Simple boolean logic prevents modal conflicts
5. **Future Extensibility**: Adding new modals requires minimal changes

## Testing Requirements

1. **Modal Opening**: Verify each modal opens correctly
2. **Modal Closing**: Verify ESC key closes modals
3. **Modal Transitions**: Verify transitioning from TimeEditModal to EditEntryModal works
4. **Keyboard Navigation**: Verify all keyboard shortcuts work within modals
5. **State Isolation**: Verify modals don't interfere with each other
6. **Main Content**: Verify main content shows when no modals are open

## Validation Commands

- Tests: `pnpm test`
- Build: `pnpm build` 
- Lint: `pnpm lint`

All existing functionality should be preserved - same keyboard shortcuts, same visual appearance, same modal interactions.
{"name": "tempo", "version": "0.0.1", "license": "Apache-2.0", "bin": "dist/cli.js", "type": "module", "engines": {"node": "22.18.0", "pnpm": "10.14.0"}, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "pnpm exec biome lint", "lint:fix": "pnpm exec biome check --write", "prepare": "husky", "clean": "turbo clean && git clean -xdf .cache .turbo dist node_modules", "test": "turbo test", "release": "auto shipit --dry-run -vv", "cli": "pnpm run --filter ./apps/cli start"}, "files": ["dist"], "devDependencies": {"@auto-it/conventional-commits": "catalog:auto", "@auto-it/git-tag": "catalog:auto", "@auto-it/npm": "catalog:auto", "@auto-it/omit-commits": "catalog:auto", "@auto-it/omit-release-notes": "catalog:auto", "@auto-it/pr-body-labels": "catalog:auto", "@auto-it/released": "catalog:auto", "@biomejs/biome": "catalog:", "@tsconfig/strictest": "catalog:", "auto": "catalog:auto", "husky": "catalog:", "lint-staged": "catalog:", "turbo": "catalog:", "typescript": "catalog:", "ultracite": "catalog:"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748", "lint-staged": {"*.{js,jsx,ts,tsx,json,jsonc,css,scss}": ["pnpm exec biome check --write --no-errors-on-unmatched"]}}